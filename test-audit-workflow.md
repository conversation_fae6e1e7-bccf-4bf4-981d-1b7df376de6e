# 二级审核功能测试指南

## 测试场景

### 1. 初审流程测试
**测试用户**: chensu3, dingtong, weishujie1 (处长角色)

**测试步骤**:
1. 使用处长账号登录系统
2. 进入任务列表页面，应该只看到状态为0的待初审任务
3. 点击任务进入审核页面
4. 验证页面显示:
   - 任务基本信息（只读）
   - 审核结果选择（通过/不通过）
   - 队伍选择区域（审核通过时显示）
   - 不显示任务详细配置（任务类型、执行类型等）
   - 不显示审核历史信息

**测试用例**:
- **初审通过**: 选择"审核通过" → 选择队伍人员 → 提交
  - 预期结果: 任务状态变为4（待二审），选择的人员信息保存
- **初审不通过**: 选择"审核不通过" → 填写原因 → 提交
  - 预期结果: 任务状态变为2（未通过），任务终止

### 2. 复审流程测试
**测试用户**: qianhongguo1 (主任角色)

**测试步骤**:
1. 使用主任账号登录系统
2. 进入任务列表页面，应该只看到状态为4的待复审任务
3. 点击任务进入审核页面
4. 验证页面显示:
   - 任务基本信息（只读）
   - 初审历史信息（审核人、结果、时间）
   - 审核结果选择（通过/不通过）
   - 队伍选择区域（预选中初审时选择的人员）
   - 任务详细配置（任务类型、执行类型、任务级别、时间类型等）

**测试用例**:
- **复审通过**: 
  1. 确认初审选择的人员已预选中
  2. 可以修改队伍人员选择
  3. 配置任务详细信息
  4. 选择"审核通过" → 提交
  - 预期结果: 任务状态变为1（进行中），任务开始执行
- **复审不通过**: 选择"审核不通过" → 填写原因 → 提交
  - 预期结果: 任务状态变为2（未通过），任务终止

### 3. 数据验证测试

**人员数据格式验证**:
- 初审提交的人员数据应该以逗号分隔的字符串格式保存
- 例如: "6205,6209,6200,6203,6211,6208,6202,6204,6206,6207,6212,6214"
- 复审时应该正确解析并预选中这些人员

**审核历史记录验证**:
- 初审信息: reviewerName, reviewResult, auditTime
- 复审信息: secondReviewerName, secondReviewResult, secondAuditTime

### 4. 权限验证测试

**角色权限测试**:
- 处长只能看到状态为0的任务
- 主任只能看到状态为4的任务
- 非审核人员不应该看到审核页面

### 5. 异常情况测试

**数据完整性测试**:
- 初审时必须选择队伍才能通过
- 复审时必须配置完整的任务信息
- 审核不通过时必须填写原因

**界面交互测试**:
- 审核结果切换时界面正确响应
- 队伍选择状态正确更新
- 表单验证正确工作

## 预期结果总结

1. **初审阶段**: 处长审核任务并选择执行队伍
2. **复审阶段**: 主任确认初审选择的队伍（可修改），配置任务详细信息
3. **数据流转**: 人员选择信息在初审和复审之间正确传递
4. **状态管理**: 任务状态按照 0 → 4 → 1 的流程正确变化
5. **权限控制**: 不同角色只能看到对应状态的任务

## 测试数据示例

```json
{
  "taskInfo": {
    "id": 35,
    "name": "测试任务",
    "status": 4,
    "person": "6205,6209,6200,6203,6211,6208,6202,6204,6206,6207,6212,6214",
    "reviewerName": "张处长",
    "reviewResult": 1,
    "auditTime": "2024-01-15 10:30:00"
  }
}
```

这个测试指南涵盖了修改后的二级审核功能的所有关键点，确保初审和复审都能正确处理队伍选择，并且复审时能够正确显示和修改初审时选择的人员。
