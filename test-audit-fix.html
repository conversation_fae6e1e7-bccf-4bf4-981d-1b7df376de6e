<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>审核功能修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            background: #f5f5f5;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        code {
            background: #f8f9fa;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: monospace;
        }
        ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        li {
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <h1>🔧 二级审核功能修复验证</h1>
    
    <div class="test-section success">
        <h2>✅ 修复完成</h2>
        <p>已成功修复 TypeScript 错误：<code>Cannot read properties of undefined (reading 'setChecked')</code></p>
        <p><strong>🔄 执行顺序优化</strong>：现在 <code>fetchTeamTreeData()</code> 在 <code>fetchTaskDetail()</code> 完成后执行</p>
    </div>

    <div class="test-section info">
        <h2>🔍 主要修复内容</h2>
        <ul>
            <li><strong>优化执行顺序</strong>：<code>fetchTeamTreeData()</code> 现在在 <code>fetchTaskDetail()</code> 完成后执行</li>
            <li><strong>添加了安全检查</strong>：在调用 <code>this.$refs.teamTree.setChecked()</code> 前检查组件是否已准备好</li>
            <li><strong>优化了预选中逻辑</strong>：使用 <code>pendingSelectedPersons</code> 暂存待处理的人员ID</li>
            <li><strong>改进了错误处理</strong>：添加 try-catch 包装，避免运行时错误</li>
            <li><strong>延迟执行机制</strong>：如果组件未准备好，自动延迟100ms重试</li>
        </ul>
    </div>

    <div class="test-section info">
        <h2>🚀 功能验证步骤</h2>
        <ol>
            <li><strong>启动开发服务器</strong>：<code>npm run dev</code> ✅ 已完成</li>
            <li><strong>编译检查</strong>：无 TypeScript 错误 ✅ 已通过</li>
            <li><strong>访问审核页面</strong>：
                <ul>
                    <li>初审页面：<code>http://localhost:8081/#/main/jointDisposal/taskAudit?id=35</code></li>
                    <li>复审页面：需要任务状态为4的数据</li>
                </ul>
            </li>
            <li><strong>测试预选中功能</strong>：
                <ul>
                    <li>确保复审时能正确显示初审选择的人员</li>
                    <li>验证人员选择状态正确更新</li>
                </ul>
            </li>
        </ol>
    </div>

    <div class="test-section warning">
        <h2>⚠️ 测试注意事项</h2>
        <ul>
            <li><strong>数据准备</strong>：需要有状态为4的任务数据用于复审测试</li>
            <li><strong>用户权限</strong>：使用正确的审核人员账号登录</li>
            <li><strong>人员数据</strong>：确保任务的 <code>person</code> 字段包含有效的人员ID</li>
            <li><strong>队伍树数据</strong>：确保 <code>/linkAge/getGroupList</code> API 返回正确的队伍结构</li>
        </ul>
    </div>

    <div class="test-section info">
        <h2>🔧 核心修复代码</h2>
        <pre><code>// 预选中人员（复审时使用）
preselectPersons(personIds) {
  if (!personIds || personIds.length === 0) {
    return;
  }

  // 检查树组件是否已经准备好
  if (!this.$refs.teamTree || !this.$refs.teamTree.setChecked) {
    // 如果树组件还没准备好，延迟执行
    setTimeout(() => {
      this.preselectPersons(personIds);
    }, 100);
    return;
  }

  // 递归查找并选中指定的人员节点
  const selectPersonsInTree = (nodes) => {
    nodes.forEach(node => {
      if (node.children && node.children.length > 0) {
        selectPersonsInTree(node.children);
      } else if (node.origin && node.origin.code) {
        const personId = parseInt(node.origin.code);
        if (personIds.includes(personId)) {
          try {
            this.$refs.teamTree.setChecked(node.nodeKey, true, false);
          } catch (error) {
            console.warn('设置节点选中状态失败:', error);
          }
        }
      }
    });
  };

  // 等待队伍树数据加载完成后再选中
  if (this.teamTreeData && this.teamTreeData.length > 0) {
    selectPersonsInTree(this.teamTreeData);
    this.handleTeamSelectionChange();
  } else {
    setTimeout(() => {
      this.preselectPersons(personIds);
    }, 100);
  }
}</code></pre>
    </div>

    <div class="test-section success">
        <h2>🎯 预期结果</h2>
        <ul>
            <li>✅ 页面加载时不再出现 TypeScript 错误</li>
            <li>✅ 复审时能正确预选中初审选择的人员</li>
            <li>✅ 队伍选择功能正常工作</li>
            <li>✅ 初审和复审都能正确提交人员信息</li>
        </ul>
    </div>

    <script>
        console.log('🔧 审核功能修复测试页面已加载');
        console.log('📝 请按照上述步骤进行功能验证');
        
        // 检查是否在开发环境
        if (window.location.hostname === 'localhost') {
            console.log('✅ 开发环境检测正常');
        } else {
            console.warn('⚠️ 当前不在开发环境，请确保服务器正常运行');
        }
    </script>
</body>
</html>
