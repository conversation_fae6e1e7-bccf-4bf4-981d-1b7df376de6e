/*
 * @Author: ya<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-04-08 14:00:22
 * @LastEditors: yangfushan <EMAIL>
 * @LastEditTime: 2025-05-07 13:55:08
 * @FilePath: \jn_jccz_web\config\dev.env.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
"use strict";
const merge = require("webpack-merge");
const prodEnv = require("./prod.env");

if ((process.argv + "").indexOf("403") > -1) {
  // 403开发环境模拟登陆用户
  module.exports = merge(prodEnv, {
    NODE_ENV: '"development"',
    DEV_TOKEN: '"d1170c6535c743905b943bb1b113ebbc"',
    NEW_PUSH_SOCKET_URL: '"wss://***********:31443/wss/websocket/"',
  });
} else {
  // 政务云开发环境模拟登陆用户token
  module.exports = merge(prodEnv, {
    NODE_ENV: '"development"',
    SDZB_SERVER: '"https://***********:39435/api"',
    HOME_SERVER: '"https://***********:39436/api"',
    HOME_WEB: '"https://***********:39436"',
    DEV_TOKEN: '"04e20e1b88b2a1a633f453bf0d9d6361"',
    NEW_PUSH_SOCKET_URL: '"wss://***********:39436/wss/websocket/"',
  });
}
