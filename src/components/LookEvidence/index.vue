<template>
  <div class="index flex sb">
    <OuterFrame style="width: 300px" title="取证记录">
      <NoData v-if="noData" />
      <Timeline v-else  class="timelist">
        <TimelineItem v-for="t in timeList" :key="t.id">
          <p
            :style="timeId == t.id ? ' color: #5585ec;' : ''"
            class="time"
            @click="handleChange(t)"
          >
            {{ moment(t.createTime).format("YYYY-MM-DD HH:mm:ss") }}
          </p>
        </TimelineItem>
        <!-- <TimelineItem>
          <p class="time">1976年11</p>
          <p class="content">Apple I 问世</p>
        </TimelineItem> -->
        <!-- <NoData text="功能完善中" /> -->
      </Timeline>
    </OuterFrame>
    <OuterFrame style="width: 300px" title="基本信息">
      <NoData v-if="noData" />
      <template v-else>
        <div
          style="width: 100%; overflow-y: auto; max-height: calc(100% - 50px)"
        >
          <div class="jc-content">
            <span class="nav">网页标题：</span>
            <span class="content">{{ jbInformation.msgTitle }}</span>
          </div>
          <div class="jc-content">
            <span class="nav">取证地址：</span>
            <span class="content">
              <span>{{ jbInformation.msgUrl }}</span>
              <p
                style="
                  width: 64px;
                  color: #5585ec;
                  cursor: pointer;
                  border-bottom: 1px solid #5585ec;
                "
                @click="copyText(jbInformation.msgUrl)"
              >
                复制链接
              </p>
            </span>
          </div>
          <div class="jc-content">
            <span class="nav">取证时间：</span>
            <span class="content">{{
              moment(jbInformation.createTime).format("YYYY-MM-DD HH:mm:ss")
            }}</span>
          </div>

          <div class="jc-content">
            <span class="nav">取证人：</span>
            <span class="content">{{ jbInformation.createUserName }}</span>
          </div>
        </div>
      </template>
    </OuterFrame>
    <OuterFrame style="width: 750px" title="取证截图">
      <div
        v-if="jbInformation.status === 1"
        style="width: 100%; overflow-y: auto; max-height: calc(100% - 50px)"
      >
        <!--        -->
        <!--         -->
        <img
          alt=""
          :src="imgUrl + jbInformation.evidenceFileUrl"
          style="
            width: 100%;
            cursor: url('../../../assets/img/放大.png'), zoom-in;
          "
          @click="blowUp"
        />
        <!--快照图片上已经有这两项-->
        <!--<div class="qz-time">
        <p>快照链接：{{jbInformation.msgUrl}}</p>
        <p>快照时间：{{moment(jbInformation.evidenceTime).format(
          "YYYY-MM-DD HH:mm:ss"
        )}}</p>
      </div>-->
      </div>
      <div v-else style="width: 100%">
        <NoData
          :text="
            !noData
              ? jbInformation.status === 0
                ? '取证中'
                : '取证失败：' + jbInformation.evidenceResult
              : ''
          "
        />
      </div>
    </OuterFrame>
    <!--    :imgList="[imgUrl + jbInformation.evidenceFileUrl]"-->
    <!--   -->
  </div>
</template>
<script>
// 这里可以导入其他文件（比如：组件，工具 js，第三方插件 js，json 文件，图片文件等等）
// 例如：import  《组件名称》  from '《组件路径》 ';
import OuterFrame from "@/views/main/eventAnalysis/eventInfo/components/outerFrame.vue";
import moment from "moment";

export default {
  data() {
    // 这里存放数据
    return {
      jbInformation: {
        msgTitle: "",
        msgUrl: "",
        createTime: "",
        createUserName: "",
      },
      timeList: [],
      imgUrl: gl.minioUrl + "/snapshot/",
      timeId: 0,
      noData: false,
    };
  },
  // import 引入的组件需要注入到对象中才能使用
  components: { OuterFrame },
  props: {
    data: {},
    pathList: {
      default: () => {},
    },
    that: {},
    moduleType:null,
  },
  // 生命周期 - 创建完成（可以访问当前this 实例）
  created() {},
  // 方法集合
  methods: {
    //图片放大
    blowUp() {
      // this.ImgPreviewStatus = true;
      this.$emit("blowUp", [this.imgUrl + this.jbInformation.evidenceFileUrl]);
      // this.$emit("blowUp", [
      //   "https://img1.baidu.com/it/u=2062152131,1998701002&fm=253&fmt=auto&app=138&f=JPEG?w=800&h=1421",
      //   "https://img1.baidu.com/it/u=2062152131,1998701002&fm=253&fmt=auto&app=138&f=JPEG?w=800&h=1421",
      // ]);
    },
    moment,
    handleChange(t) {
      this.timeId = t.id;
      this.getData(t.id);
    },
    // 复制信息
    copyText(d) {
      try {
        navigator.clipboard.writeText(d);
        this.$Message.success("信息已复制");
      } catch (err) {
        this.$Message.error("无法复制信息");
      }
    },
    // 获取右侧数据
    getData(id) {
      let params = {
        id: id, //取证主键id
      };
      this.that.$http
        .get("/recommend/evidenceMsgById", { params })
        .then((res) => {
          let result = res.body;
          if (result.status == 0) {
            this.jbInformation = result.data;
          }
        });
    },
    // 获取左侧时间列表
    getTimeList() {
      // debugger;
      console.log(this.that.$route.path);
      let type = 1;
      if(this.that.$route.query.sourcePath){

        console.log(JSON.stringify(this.pathList[this.that.$route.query.sourcePath].type));
        
        if (this.pathList[this.that.$route.query.sourcePath] && this.pathList[this.that.$route.query.sourcePath].type) {
          type = this.pathList[this.that.$route.query.sourcePath].type;
        } else if(this.pathList[this.that.$route.path] && this.pathList[this.that.$route.path].type) {
          type = this.pathList[this.that.$route.query.sourcePath].type;
        } else {
          type = this.data.type;
        }
        
     }else{
      //联动处置接口带入
      type=this.data.type;
     }
      if (!type) {
          type = 1;
        }
      
      let params = {
        mkey: this.data.mkey || this.data.msgKey, //数据mkey
        type: type //1 涉济，2 涉鲁，3涉政
      };
      this.that.$http
        .get("/recommend/evidenceMsgList", { params })
        .then((res) => {
          let result = res.body;
          if (result.status == 0) {
            this.timeList = result.data;
            if (this.timeList.length > 0) {
              this.getData(this.timeList[0].id);
              this.timeId = this.timeList[0].id;
            } else {
              this.noData = true;
            }
          }
        });
    },
  },
  // 计算属性 类似于 data 概念
  computed: {},
  // 监控 data 中的数据变化
  watch: {},
  //过滤器
  filters: {},
  // 生命周期 - 挂载完成（可以访问 DOM 元素）
  mounted() {
    this.getTimeList();
  },
};
</script>
<style lang="less" scoped>
 /deep/ .ivu-timeline-item-head {
    top: 3px;
  }
.timelist{
  overflow-y: scroll;
  max-height: ~"calc(100% - 50px )";
}
.index {
  width: 1400px;
  height: 700px;
}

.time {
  color: #606d7d;
  cursor: pointer;
  font-size: 16px;
  font-weight: 600;
}

.jc-content {
  display: flex;
  align-items: baseline;
  margin-bottom: 20px;
  font-size: 16px;

  .nav {
    font-weight: 600;
    color: #ced2d9;
    width: 80px;
    white-space: nowrap;
  }

  .content {
    flex: 1;
    color: #606d7d;
  }
}

.qz-time {
  position: absolute;
  top: 50px;
  left: 60px;

  p {
    color: #5585ec;
    font-size: 16px;
    margin-top: 10px;
  }
}
</style>
