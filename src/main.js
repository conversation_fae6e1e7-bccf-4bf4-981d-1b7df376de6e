import Vue from "vue";
import router from "./router";
import Main from "./views/index.vue";
import common from "./assets/js/common";
import permission from "./assets/js/permission";
import VueClipboards from "vue-clipboard2";
import VueCookies from "vue-cookies";
import infiniteScroll from "vue-infinite-scroll";
import NoData from "@/components/noData";
import frameName from "@/components/frameName.vue"; //顶部title组件
import RichTextEditor from "@/components/richTextEditor"; //富文本编辑器组件
import axios from "@/util/axios.js";
import { createPinia, PiniaVuePlugin } from "pinia"; //vue2.7 需要引入该插件
import directives from "./directives"; //复制图片插件
import pinia from "./stores";
import html2canvas from "html2canvas";
import Clipboard from "clipboard";

Vue.use(PiniaVuePlugin); //安装插件
Vue.use(directives); //复制图片
// Vue.use(pinia);

import clamp from "./components/clamp";

Vue.config.productionTip = false;

Vue.directive("clamp", clamp);
new Vue({
  el: "#app",
  // 其他配置...
  // ...
  // 请注意，同一个`pinia'实例
  // 可以在同一个页面的多个 Vue 应用中使用。
  pinia: createPinia(), //注意如果直接使用pinia:createPinia(),那么在其他地方使用的pinia的实例就不相同了。
});
Vue.use(infiniteScroll);
import "./icons";
import "video.js/dist/video-js.css";

Vue.use(VueCookies);
Vue.use(VueClipboards);

Vue.component("FrameName", frameName);
Vue.component("NoData", NoData);
Vue.component("RichTextEditor", RichTextEditor);
common.init(Vue, router);
common.loadingBar(router);
permission.init(Vue, router);
import "./components/GlobalModal/index.js";
new Vue({
  el: "#main", // 入口位置，在相应位置替换为组件
  router, // 路由信息，根据配置跳转相应组件，等于router:router
  render: (h) => h(Main), // 指定模板组件
});
Vue.prototype.$axios = axios;
Vue.prototype.openBlank = function (path) {
  window.open(path, "_blank");
};
Vue.prototype.takeScreenshot = (element, name = "图片") => {
  html2canvas(element, {
    scale: window.devicePixelRatio * 2,
    dpi: 350,
  }).then((canvas) => {
    // 将 canvas 转换为图片
    const imgData = canvas.toDataURL("image/png");
    // 创建一个链接，用于下载
    const link = document.createElement("a");
    link.href = imgData;
    link.download = name; // 指定文件名
    link.click();
  });
};
Vue.prototype.getLog = function (module, content, spark) {
  let url = "/login/addLog";
  let params = {
    module: module,
    content: content ? content : "浏览",
    spark: spark,
  };
  return this.$http.get(url, { params });
};

Vue.prototype.historyLog = function (module, content,msgKey) {
  if (!module){
    return ;
  }
  let url = "/msgDeal/addHistoryLog";
  let params = {
    module: module,
    content: content ? content : "浏览",
    msgKey: msgKey,
  };
  return this.$http.get(url, { params });
};

// 上传图片前缀
Vue.prototype.imgBaseUrl = gl.minioUrl + "/jnservermsg/";
Vue.prototype.videoBaseUrl = gl.minioUrl + "/video/";
Vue.prototype.imageBaseUrl = gl.minioUrl + "picture/";
// 调sdzb get请求接口
Vue.prototype.getSdzb = function (url, params, options) {
  return this.$http.get(process.env.SDZB_SERVER + url, { params }, options);
};

// 调sdzb postt请求接口
Vue.prototype.postSdzb = function (url, params, options) {
  return this.$http.post(process.env.SDZB_SERVER + url, params, options);
};

Vue.prototype.copyUrl = function (e, text) {
  const clipboard = new Clipboard(e.target, { text: () => text });
  clipboard.on("success", (e) => {
    this.$Message.success("复制成功");
    // 释放内存
    clipboard.off("error");
    clipboard.off("success");
    clipboard.destroy();
  });
  clipboard.on("error", (e) => {
    // 不支持复制
    this.$Message.error("复制失败");
    // 释放内存
    clipboard.off("error");
    clipboard.off("success");
    clipboard.destroy();
  });
  clipboard.onClick(e);
};
