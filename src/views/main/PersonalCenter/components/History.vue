<template>
  <div class="Collection">
    <div class="filter flex sb">
      <div class="item flex">
        <div class="label flex">
          <svg-icon icon-class="榜单-时间" />
          时间：
        </div>
        <div class="options flex">
          <div
            v-for="item in timeOption"
            :key="item.key"
            :class="['item', 'cp', dayNum === item.key ? 'active' : '']"
            @click="dayNum = item.key"
          >
            {{ item.name }}
          </div>
          <DatePicker
            v-if="dayNum === '-99'"
            v-model="timeFrame"
            format="yyyy-MM-dd HH:mm"
            placeholder="请选择时间范围"
            style="width: 300px;"
            type="datetimerange"
            split-panels
            @on-change="dateChange"
          ></DatePicker>
        </div>
      </div>
      <div class="item flex">
        <Input
          v-model="keyword"
          placeholder="请输入您要查找的内容"
          style="width: 200px; margin: 0 10px;"
          @on-enter="search"
        >
        </Input>
        <div class="btn cp" @click="search">
          搜索
        </div>
      </div>
    </div>
    <div class="table">
      <div class="header flex sb">
        <div class="item">
          序号
        </div>
        <div class="item">
          操作时间
        </div>
        <div class="item">
          一级模块
        </div>
        <div class="item">
          二级模块
        </div>
        <div class="item">
          内容
        </div>
        <div class="item">
          操作
        </div>
      </div>
      <div class="list">
        <Spin v-if="loading">
          <Icon class="demo-spin-icon-load" size="18" type="ios-loading"></Icon>
          <div>Loading</div>
        </Spin>
        <NoData v-if="!loading && total === 0 && listData.length === 0" />
        <div
          class="row flex sb"
          v-for="(item, index) in listData"
          :key="item.id"
        >
          <div class="item">
            {{ (pageNo - 1) * pageSize + index + 1 }}
          </div>
          <div class="item">
            {{ moment(item.publishTime).format("YYYY-MM-DD HH:mm:ss") }}
          </div>
          <div class="item">
            {{ item.module1 }}
          </div>
          <div class="item">
            {{ item.module2 }}
          </div>
          <div class="item">
            {{ item.mcontent }}
          </div>
          <div class="item flex">
            <div class="btn" @click="Operation(item.id)">
              <!-- <svg-icon icon-class="收藏蓝" /> -->
              删除记录
            </div>
          </div>
        </div>
        <Page
          v-if="total > 0 && !loading"
          :total="total"
          @on-change="pageNoChange"
          @on-page-size-change="pageSizeChange"
          :current="pageNo"
          :page-size="pageSize"
          show-elevator
          show-sizer
          show-total
        />
      </div>
    </div>
  </div>
</template>

<script>
const timeOption = [
  {
    name: "全部",
    key: "all",
  },
  {
    name: "今日",
    key: "1",
  },
  {
    name: "近一日",
    key: "98",
  },
  {
    name: "近三日",
    key: "3",
  },
  {
    name: "近一周",
    key: "7",
  },
  {
    name: "近一月",
    key: "30",
  },
  {
    name: "自定义",
    key: "-99",
  },
];

import moment from "moment/moment";
export default {
  name: "",
  data() {
    return {
      timeOption,
      dayNum: "all",
      keyword: "",
      loading: false,
      total: 0,
      listData: [],
      pageNo: 1,
      pageSize: 20,
    };
  },
  created() {
    this.getDataCount();
  },
  methods: {
    moment,
    Operation(id) {
      this.$http
        .get("/msgDeal/deleteHistory", { params: { id: id } })
        .then((res) => {
          this.$Message.success("取消收藏成功");
          this.getDataList();
        });
    },
    pageNoChange(d) {
      this.pageNo = d;
      this.getDataList();
    },
    pageSizeChange(d) {
      this.pageNo = 1;
      this.pageSize = d;
      this.getDataList();
    },
    getParams() {
      let params = {};
      if (this.dayNum === "-99" && !this.timeFrame[0]) {
        return false;
      }
      if (this.dayNum === "-99" && this.timeFrame[0]) {
        params.startTime = moment(this.timeFrame[0]).format(
          "YYYY-MM-DD HH:mm:ss"
        );
        params.endTime = moment(this.timeFrame[1]).format(
          "YYYY-MM-DD HH:mm:ss"
        );
      } else {
        params.dayNum = this.dayNum === "all" ? null : this.dayNum;
      }
      return {
        ...params,
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        keyword: this.keyword,
      };
    },
    getDataCount() {
      this.listData = [];
      this.pageNo = 1;
      this.total = 0;
      let params = this.getParams();
      if (!params) {
        this.$Message.warning("请选择时间范围后重试");
        return false;
      }
      this.$http.get("/msgDeal/historyCount", { params }).then((res) => {
        this.total = res.body.data || 0;
      });
      this.getDataList();
    },
    getDataList() {
      this.loading = true;
      let params = this.getParams();
      this.listData = [];
      this.$http.get("/msgDeal/historyList", { params }).then((res) => {
        console.log(res);

        if (res.body.data && res.body.data.length > 0) {
          this.listData = res.body.data;
        }
        this.loading = false;
      });
    },
    search() {
      this.keyword = this.keyword;
      this.getDataCount();
    },
    dateChange(date) {
      this.search();
      console.log(date);
    },
  },
  watch: {
    dayNum: {
      handler(val) {
        if (val !== "-99") {
          this.search();
          this.timeFrame = null;
        }
      },
    },
  },
};
</script>

<style lang="less" scoped>
.Collection {
  .filter {
    height: 80px;
    align-items: center;
    padding: 0 20px;
    .item {
      &:first-child {
        flex: 1;
      }
      .svg-icon {
        width: 25px;
        height: 25px;
        margin-right: 10px;
      }

      .label {
        align-items: center;
      }

      .options {
        height: 30px;
        align-items: center;

        .item {
          margin-right: 10px;
        }

        .active {
          color: #5585ec;
        }
      }
    }
  }
  .table {
    .header {
      background-color: #fafafa;
      align-items: center;
      border-bottom: 2px solid #e5e5e5;
      .item {
        padding-top: 20px;
        width: 100px;
        height: 80px;
        line-height: 40px;
        text-align: center;
        &:nth-child(2) {
          width: 200px;
        }
        &:nth-child(3) {
          width: 200px;
        }
        &:nth-child(4) {
          width: 200px;
        }
        &:nth-child(5) {
          flex: 1;
        }
        &:nth-child(6) {
          width: 200px;
        }
      }
    }
    .list {
      position: relative;
      height: calc(~"100vh - 230px");
      overflow-y: auto;
      .row {
        align-items: center;
        border-bottom: 2px solid #e5e5e5;
        .item {
          width: 100px;
          height: 60px;
          line-height: 60px;
          text-align: center;
          .btn {
          }
          &:nth-child(2) {
            width: 200px;
          }
          &:nth-child(3) {
            width: 200px;
          }
          &:nth-child(4) {
            width: 200px;
          }
          &:nth-child(5) {
            flex: 1;
          }
          &:nth-child(6) {
            width: 200px;
            align-items: center;
            justify-content: center;
          }
        }
      }
    }
  }
}
</style>
