<template>
  <div class="frame flex sb">
    <div class="userInfo">
      <div class="ProfilePicture">
        <img src="@/assets/img/image.png" alt="" />
      </div>
      <div class="userName">
        <span>{{ userName }}（{{ userAccount }}）</span>
      </div>
      <div class="unit">
        {{ organName }}
      </div>
      <div class="phone">
        {{ iphone }}
      </div>

      <div class="cptFrame">
        <div
          class="item flex sb cp"
          :class="[moduleId == 'Collection' ? 'active' : '']"
          @click="moduleId = 'Collection'"
        >
          <svg-icon
            :icon-class="moduleId == 'Collection' ? '已收藏1' : '已收藏'"
            style="width: 26px; height: 26px;"
          />
          <div>
            收藏
          </div>
        </div>
        <div
          class="item flex sb cp"
          :class="[moduleId == 'History' ? 'active' : '']"
          @click="moduleId = 'History'"
        >
          <svg-icon
            :icon-class="moduleId == 'History' ? '历史1' : '历史'"
            style="width: 26px; height: 26px;"
          />
          <div>
            历史
          </div>
        </div>
      </div>
    </div>
    <div class="content">
      <component :is="moduleId" />
    </div>
  </div>
</template>

<script>
import Collection from "./components/Collection.vue";
import History from "./components/History.vue";

export default {
  name: "",
  data() {
    return {
      moduleId: "Collection",
      organName: localStorage.getItem("organName"),
      iphone: localStorage.getItem("iphone"),
      userAccount: localStorage.getItem("userAccount"),
      userName: localStorage.getItem("userName"),
    };
  },
  components: {
    Collection,
    History,
  },
};
</script>

<style lang="less" scoped>
.frame {
  height: 100%;
  padding: 20px 20px 20px 0;

  .userInfo {
    width: 300px;
    height: 100%;
    background-color: #fff;
    padding: 100px 0 0 0;
    .ProfilePicture {
      width: 100px;
      height: 100px;
      border-radius: 50%;
      overflow: hidden;
      margin: 0 auto;
      img {
        width: 100%;
        height: 100%;
      }
    }
    div {
      text-align: center;
    }
    .userName {
      margin-top: 20px;
    }
    .unit,
    .phone {
      color: #b8b8b8;
      margin-top: 10px;
    }
    .cptFrame {
      margin-top: 20px;
      padding: 0 20px;
      .item {
        width: 100%;
        height: 60px;
        border-radius: 5px;
        align-items: center;
        padding: 0 80px;
        font-size: 20px;
        font-weight: 600;
        margin-bottom: 10px;
      }
      .active {
        background-color: #edf0fd;
        color: #5f81fb;
      }
    }
  }

  .content {
    flex: 1;
    margin-left: 20px;
    background-color: #fff;
  }
}
</style>
