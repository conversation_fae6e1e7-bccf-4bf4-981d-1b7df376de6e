<template>
    <div class="hot_list">
        <div class="head_box">
            <div class="header flex sb">
                <div class="Toggle flex">
                    <!-- @click="typeChange(i.key)" -->
                    <div
                        v-for="i in typeList"
                        :key="i.key"
                        :class="[
                            'cp',
                            'flex',
                            'item',
                            type === i.key ? 'active' : '',
                        ]"
                        @click="typeChange(i.key)"
                    >
                        {{ i.name }}

                        <div class="triangle"></div>
                    </div>
                </div>
            </div>
        </div>
        <component :is="componentName"></component>
    </div>
</template>

<script>
const typeList = [
    {
        key: "3",
        name: "济南同城榜",
    },
    {
        key: "1",
        name: "涉济热榜监测",
    },
    {
        key: "2",
        name: "国内热榜监测",
    },
];
import chinaHotList from "../chinaHotList";
import jiNanHotList from "../jiNanHotList";
import sameCountry from "../sameCountry";
export default {
    data() {
        return {
            typeList,
            type: "1",
        };
    },
    components: {
        jiNanHotList,
        chinaHotList,
        sameCountry
    },
    methods: {
        typeChange(d) {
            this.type = d;
            // 涉及推荐
            if (d == "1") {
                this.$route.meta.moduleName = "涉济监测/涉济热榜监测";
            } else if (d == "2") {
                this.$route.meta.moduleName = "涉济监测/国内热榜监测";
            }
        },
    },
    computed: {
        componentName() {
            return this.type == "1"
                ? "jiNanHotList"
                : this.type == 2
                ? "chinaHotList"
                : "sameCountry";
        },
    },
};
</script>

<style lang="less" scoped>
.head_box {
    padding: 18px;
    background-color: #fff;
    .header {
        border-bottom: 2px solid #537be6;
        height: 50px;
        color: #666666;
        font-size: 14px;
        .Toggle {
            .active {
                color: #537be6;

                .triangle {
                    display: block !important;
                }
            }

            .item {
                position: relative;
                line-height: 25px;
                align-items: center;
                margin-right: 40px;

                .svg-icon {
                    width: 25px;
                    height: 25px;
                }

                .triangle {
                    display: none;
                    position: absolute;
                    bottom: 0;
                    left: 50%;
                    transform: translateX(-50%);
                    width: 0;
                    height: 0;
                    border-left: 8px solid transparent;
                    border-right: 8px solid transparent;
                    border-bottom: 8px solid #537be6;
                }
            }
        }

        .Tips {
            align-items: center;

            & > div {
                margin-left: 20px;
            }
        }
    }
}
</style>
