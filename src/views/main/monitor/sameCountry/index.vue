<template>  
    <div class="listwrap">
        <div class="box">
            <div class="tip">
                <div class="blue"></div>
                <div>微博同城榜</div>
            </div>
            <div v-show="!weiloading && weilist.length>0" class="list">
                <div v-for="(item,index) in weilist" :key="index" class="listitem">
                    <span>{{index+1}}</span>
                    <span class="title" @click="toSearch(item.keyword)">{{item.keyword}}</span>
                </div>
            </div>
            <div v-show="weiloading" class="list">
                <Spin >
                    <Icon class="demo-spin-icon-load" size="18" type="ios-loading"></Icon>
                    <div>Loading</div>
                </Spin>
            </div>
            <div v-show="weilist.length === 0 && !weiloading" class="list">
                <no-data />
            </div>
        </div>
        <div class="box" >
            <div class="tip">
                <div class="blue"></div>
                <div>抖音同城榜</div>
            </div>
            <div v-show="!douloading && doulist.length>0" class="list">
                <div v-for="(item,index) in doulist" :key="index" class="listitem">
                    <span>{{index+1}}</span>
                    <span class="title" @click="toSearch(item.keyword)">{{item.keyword}}</span>
                </div>
            </div>
            <div v-show="douloading" class="list">
                <Spin>
                    <Icon class="demo-spin-icon-load" size="18" type="ios-loading"></Icon>
                    <div>Loading</div>
                </Spin>
            </div>
            <div v-show="doulist.length === 0 && !douloading" class="list">
                <no-data/>
            </div>
        </div>
        <div class="box">
            <div class="tip">
                <div class="blue"></div>
                <div>头条同城榜</div>
            </div>
            <div v-show="!touloading && toulist.length>0" class="list">
                <div v-for="(item,index) in toulist" :key="index" class="listitem">
                    <span>{{index+1}}</span>
                    <span class="title" @click="toSearch(item.keyword)">{{item.keyword}}</span>
                </div>
            </div>
            <div v-show="touloading" class="list">
                <Spin>
                    <Icon class="demo-spin-icon-load" size="18" type="ios-loading"></Icon>
                    <div>Loading</div>
                </Spin>
            </div>
            <div v-show="toulist.length === 0 && !touloading" class="list">
                <no-data />
            </div>
        </div>
    </div>
</template>

<script>
export default {
    data() {
        return {
            toulist:[],
            weilist:[],
            doulist:[],
            touloading:true,
            weiloading:true,
            douloading:true,
        }
    },
    methods: {
        toSearch(keyword) {
            const { href } = this.$router.resolve({
                path: "/main/comprehensiveSearch/searchResults",
                query: {
                keyword: keyword,
                type: 1,
                dayNum: 31,
                },
            });
            window.open(href, "_blank");
        },
        gettouData() {
            this.touloading = true;
            const params = {
                type: 2,
            };
            params.sources = '7';
            
            this.$http.get("/home/<USER>", { params }).then((res) => {
                if(res.body.status === 0) {
                    this.toulist = res.body.data
                    this.touloading = false
                }
                console.log('toulisttoulisttoulist',this.toulist);
            });
        },
        getweiData() {
            this.weiloading = true;
            const params = {
                type: 2,
            };
            params.sources = '8';
            
            this.$http.get("/home/<USER>", { params }).then((res) => {
                if(res.body.status === 0) {
                    this.weilist = res.body.data
                    this.weiloading = false
                }
                console.log('weilistweilistweilist'.this.weilist);
            });
        },
        getdouData() {
            this.douloading = true;
            const params = {
                type: 2,
            };
            params.sources = '9';
            
            this.$http.get("/home/<USER>", { params }).then((res) => {
                if(res.body.status === 0) {
                    this.doulist = res.body.data
                    this.douloading = false
                }
                console.log('doulistdoulist',this.doulist);
            });
        },
    },
    mounted() {
        this.gettouData()
        this.getweiData()
        this.getdouData()
    }

}
</script>

<style lang="less" scoped>
.listwrap {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
    .box {
        height: 100%;
        padding: 10px 20px;
        width: 570px;
        background-color: #fff;
        .tip {
            height: 48px;
            display: flex;
            align-items: center;
            border-bottom:1px solid #faf7fa;

        }
        .blue {
            margin-right: 10px;
            width: 6px;
            height: 24px;
            background-color: #5585ec;
        }
        .list {
            height: calc(100vh - 27px);
            overflow-y: auto;
            .listitem {
                display: flex;
                align-items: center;
                height: 48px;
                border-bottom:1px solid #faf7fa;
                .title {
                    margin-left: 8px;
                    display: inline-block;
                    flex: 1;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }
            }
        }
    }
}
</style>