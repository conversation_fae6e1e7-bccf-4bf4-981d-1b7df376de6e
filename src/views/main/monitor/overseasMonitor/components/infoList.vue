<template>
  <div class="infoList">
    <div v-if="total !== 0" class="listBox">
      <div v-for="(item, index) in listData" :key="index" class="item">
        <div class="SerialNum">{{ index + 1 + (pageNo - 1) * pageSize }}.</div>
        <div class="content">
          <template v-if="item.situation != 400">
            <div class="title ellipsis cp" @click="toDetails(item)">
              <PlatformIcon :id="item.msgChannelId.indexOf('facebook') > -1
                ? 501
                : item.msgChannelId.indexOf('twitter') > -1
                  ? 502
                  : item.situation
                " />
              <span v-if="!tranStatus" v-html="highlightTool.highlightByHitWords(
                item.mtitle,
                item.matchWords ? item.matchWords.split(',') : [],
                'highlight0'
              )
                "></span>
              <span v-else v-html="highlightTool.highlightByHitWords(
                item.mtitleTrans ? removeROrN(item.mtitleTrans) : '无译文',
                item.matchWords ? item.matchWords.split(',') : [],
                'highlight0'
              )
                "></span>
            </div>
            <div class="abstract ellipsis cp" @click="toDetails(item)"
              v-if="item.mtitleTrans && item.mtitleTrans != item.mtitle" style="color: #a2a1a1;">
              <svg-icon icon-class="已翻译" style="margin-right: 6px; width: 16px; height: 16px;" />
              <span v-html="highlightTool.highlightByHitWords(
                item.mtitleTrans ? removeROrN(item.mtitleTrans) : '',
                item.matchWords ? item.matchWords.split(',') : [],
                'highlight0'
              )
                "></span>
            </div>
          </template>
          <template v-else>
            <div class="abstract ellipsis-2 cp" style="min-height: 26px; max-height: 48px;" @click="toDetails(item)">
              <PlatformIcon :id="item.msgChannelId.indexOf('facebook') > -1
                ? 501
                : item.msgChannelId.indexOf('twitter') > -1
                  ? 502
                  : item.situation
                " />
              <span v-if="!tranStatus" v-html="highlightTool.highlightByHitWords(
                item.mtitle
                  ? removeROrN(item.mtitle)
                  : removeROrN(item.mcontent),
                item.matchWords ? item.matchWords.split(',') : [],
                'highlight0'
              )
                "></span>
              <span v-else v-html="highlightTool.highlightByHitWords(
                item.mtitleTrans ? removeROrN(item.mtitleTrans) : '无译文',
                item.matchWords ? item.matchWords.split(',') : [],
                'highlight0'
              )
                "></span>
            </div>

            <div class="abstract ellipsis-2 cp" style="min-height: 24px; max-height: 48px; color: #a2a1a1;"
              @click="toDetails(item)" v-if="item.mtitleTrans && item.mtitleTrans != item.mtitle">
              <svg-icon icon-class="已翻译" style="margin-right: 6px; width: 16px; height: 16px;" />
              <span v-html="highlightTool.highlightByHitWords(
                item.mtitleTrans ? removeROrN(item.mtitleTrans) : '',
                item.matchWords ? item.matchWords.split(',') : [],
                'highlight0'
              )
                "></span>
            </div>
          </template>
          <div class="remarksOperations">
            <div class="remarks">
              <div>
                {{ item.mpublishTime }}
              </div>
              <div>
                <svg-icon icon-class="媒体类型" />
                {{
                (situation[item.situation] && item.situation != 600 && item.situation != 80 ? situation[item.situation] : "") +
                (situation[item.situation] && item.situation != 600 && item.situation != 80 ? "-" : "") +
                (item.mwebsiteName ? item.mwebsiteName : "")
                }}
              </div>
              <div v-if="item.uname">
                <Icon type="ios-contact" />
                {{ item.uname }}
              </div>
              <!-- <ListControls :data="item" @PushNotification="PushNotification" /> -->
            </div>
            <ListControls style="float: right;" :data="item" @PushNotification="PushNotification" />
            <!-- <ListControls :btnStatus="btnStatus" :data="item" /> -->
          </div>
        </div>
      </div>
    </div>
    <Spin v-if="loading" fix>
      <Icon class="demo-spin-icon-load" size="18" type="ios-loading"></Icon>
      <div>Loading</div>
    </Spin>
    <no-data v-if="total === 0 && !loading" />

    <Page v-show="total > 0 && !loading" :current="pageNo" :page-size="pageSize" :total="total" show-elevator show-sizer
      show-total @on-change="pageNoChange" @on-page-size-change="pageSizeChange" />
  </div>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具 js，第三方插件 js，json 文件，图片文件等等）
// 例如：import  《组件名称》  from '《组件路径》 ';
import highlightTool from "trs-tool-highlight";

import ListControls from "@/views/main/OffshoreMonitoring/recommended/components/ListControls.vue";
import PlatformIcon from "@/components/platformIcon/index.vue";

const situation = {
  0: "全部",
  30: "新闻",
  31: "新闻app",
  10: "微博",
  20: "微信公号",
  80: "小红书",
  61: "论坛",
  62: "贴吧",
  170: "知乎",
  199: "短视频",
  200: "短视频",
  230: "自媒体",
};
export default {
  data() {
    // 这里存放数据
    return {
      situation,
      listData: [],
      pageNo: 1,
      pageSize: 20,
      loading: true,
      total: 0,
    };
  },
  // import 引入的组件需要注入到对象中才能使用
  components: { PlatformIcon, ListControls },
  props: {
    params: {
      default: {},
    },
    tranStatus: {
      default: false,
    },
  },
  // 生命周期 - 创建完成（可以访问当前this 实例）
  created() { },
  // 方法集合
  methods: {
    PushNotification(data) {
      data.mtitleTran = data.mtitleTrans;
      this.$http
        .post("/abroad/monitor/addRecommendMsg", data, {
          emulateJSON: true,
        })
        .then((res) => {
          if (res.body.status == 0) {
            this.$Message.success("推送成功");
          } else {
            this.$Message.warning("推送失败");
          }
        });
    },
    removeROrN(str) {
      if (str) {
        return str
          .replace(/(\r\n|\n|\r|\\r\\n|\\r|\\n)/g, "")
          .replace(/<[^>]+>/g, "")
          .trim();
      } else {
        return "";
      }
    },
    toDetails(d) {
      const { href } = this.$router.resolve({
        path: "/main/details",
        query: {
          msgKey: d.mkey,
          keyword: d.matchWords,
          situation: d.situation,
          sourcePath: this.$route.path,
          sourceName: this.$route.name,
          moduleOriginName:
            this.$route.meta && this.$route.meta.moduleName
              ? encodeURIComponent(this.$route.meta.moduleName)
              : "",
          moduleName:
            this.$route.meta && this.$route.meta.moduleName
              ? encodeURIComponent(
                this.$route.meta.moduleName.split("/")[0] + "/正文页"
              )
              : "",
        },
      });
      window.open(href, "_blank");
    },
    pageSizeChange(d) {
      this.pageNo = 1;
      this.pageSize = d;
      this.getCount();
    },
    pageNoChange(d) {
      this.pageNo = d;
      this.getListData();
    },
    getCount() {
      this.pageNo = 1;
      this.listData = [];
      this.total = 0;
      let ruleIds = this.params.ruleIds ? this.params.ruleIds : [];
      if (this.params.typeId == 3 && !ruleIds) {
        this.$Message.warning("请至少勾选一项");
        return;
      }
      this.loading = true;
      let params = {
        ...this.params,
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        ruleIds: ruleIds,
        transExists: true,
      };
      if (params.dayNum == null) {
        params.dayNum = "-1";
      }
      if (params.dayNum == 2) {
        params.dayNum = "98";
      }
      if (!params.situations) {
        params.situations = JSON.parse(JSON.stringify(["400", "501", "502"]));
      }

      this.$http
        .post("/abroad/msgCount", params)
        .then((res) => {
          console.log(res);
          this.loading = false;
          if (res.body.status === 0 && res.body.data) {
            this.total = res.body.data.total;
            // this.getListData();
            this.listData = !res.body.data.list ? [] : res.body.data.list;
          } else {
            this.total = 0;
            this.listData = [];
          }
        })
        .catch((err) => {
          this.loading = false;
          this.$Message.error("服务器错误！");
        });
    },
    getListData() {
      this.listData = [];
      this.loading = true;
      let params = {
        ...this.params,
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        ruleIds: this.params.ruleIds ? this.params.ruleIds : [],
        emotions:
          this.params.emotions === "3"
            ? 0
            : this.params.emotions === "2"
              ? -1
              : this.params.emotions === "1"
                ? 1
                : null,
        transExists: true,
      };
      if (params.typeId === "2") {
        params.murl = params.keyword;
        params.keyword = null;
      }
      if (params.dayNum == null) {
        params.dayNum = "-1";
      }
      if (params.dayNum == 2) {
        params.dayNum = "98";
      }
      if (!params.situations) {
        params.situations = JSON.parse(JSON.stringify(["400", "501", "502"]));
      }

      this.$http
        .post("/abroad/msgList", params)
        .then((res) => {
          if (res.body.status === 0) {
            this.listData = res.body.data;
          }
          this.loading = false;
        })
        .catch((err) => {
          this.loading = false;
          this.$Message.error("服务器错误！");
        });
    },
  },
  // 计算属性 类似于 data 概念
  computed: {
    highlightTool() {
      return highlightTool;
    },
  },
  // 监控 data 中的数据变化
  watch: {
    tranStatus: {
      handler(d) { },
    },
    params: {
      handler(d) {
        if (d) {
          this.getCount();
        } else {
          this.loading = false;
        }
      },
      deep: true,
    },
  },
  //过滤器
  filters: {},

  // 生命周期 - 挂载完成（可以访问 DOM 元素）
  mounted() { },
};
</script>

<style lang="less" scoped>
.infoList {
  background-color: #fff;
  margin-top: 20px;
  border-radius: 8px;
  position: relative;
  padding: 20px;
  //height: calc(~"100vh - 330px");
  height: calc(~"100vh - 250px");

  .listBox {
    //height: calc(~"100vh - 410px");
    height: calc(~"100vh - 340px");
    padding: 10px 5px;
    overflow-y: auto;
  }

  .item {
    display: flex;
    justify-content: space-between;
    height: 130px;
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.16);
    margin-top: 12px;
    padding: 10px;
  }

  .content {
    font-size: 16px;
    width: calc(~"100% - 50px");

    .title {
      font-weight: 600;
      line-height: 22px;
    }

    .remarksOperations {
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: #666666;
      font-size: 14px;
      margin-top: 20px;

      .remarks {
        display: flex;
        line-height: 22px;

        &>div {
          margin-right: 30px;
          display: flex;
          align-items: center;
        }

        .line {
          width: 0;
          height: 22px;
          border: 1px solid #707070;
        }
      }
    }
  }
}
</style>
