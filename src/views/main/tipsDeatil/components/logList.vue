<template>
  <div >
    <div v-for="(k,index) in data" :key="k.logId" class="log-content">
      <div style="width:235px;">
        <div class="time">{{moment(k.createTime).format("YYYY-MM-DD HH:mm:ss")}}</div>
        <div class="time ellipsis" style="color:#999999;" :title="k.createUserName + '（' + k.organName + '）'">{{k.createUserName + '（' + k.organName + '）'}}</div>
       </div>
       <span class="xian">
        <div class="xians">
        <div v-if="index == 0 || index == data.length - 1" class="by left50"></div>
        <div class="yuan left50" v-else></div>
        <div class="xc left50" v-if="index != data.length - 1"></div>
        </div>
       </span>
      
        <div style="color:#537be6;min-width:120px;" v-if="![5,4].includes(k.logStatus)">
        <div style="text-align:left;">{{getName(k.logStatus)}}</div>
       </div>
       <div style="color:#537be6;min-width:120px;" v-if="[5,4].includes(k.logStatus)" class="bk">
        <div >{{getName(k.logStatus)}}</div>
         <div class="bk-h">
            <p>{{k.logStatus == 5 ? '退回原因：':k.logStatus == 4? '反馈内容：':'关联事件'}}</p>
            <p style="width:240px;min-height:20px;font-size:16px;">{{k.logContent?k.logContent:''}}
              <span @click="copyText(k.logContent)" style="color: #5585ec;cursor: pointer" v-if="k.logContent">复制</span>
            </p>
        </div>
       </div>
      
    </div>
    

  </div>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具 js，第三方插件 js，json 文件，图片文件等等）
// 例如：import  《组件名称》  from '《组件路径》 '
import moment from "moment";
export default {
  name: "index.vue",
  data() {
    // 这里存放数据
    return {}
  },
  // import 引入的组件需要注入到对象中才能使用
  components: {},
  props: {
    data: {
        default :() => []
    },
  },
  // 方法集合
  methods: {
    moment,
    getName(d) {
    let str = ''
    if (d == 1 || d == 2) {
      str = '提示单下发'
    } else if(d == 3) {
       str = '提示单接收' 
    } else if(d == 4) {
       str = '提示单反馈' 
    } else if(d == 5) {
       str = '提示单退回' 
    } else if(d == 6) {
       str = '完结提示单' 
    } else if(d == 8) {
       str = '加入素材库' 
    } else if(d == 9) {
       str = '关联事件' 
    } else if(d == 10) {
       str = '短信通知' 
    } else if(d == 11) {
       str = '提示单转办' 
    }else if(d == 13) {
       str = '关联提示单' 
    }
    return str
    },
    // 复制信息
    copyText(d) {
      try {
        navigator.clipboard.writeText(d);
        this.$Message.success("信息已复制");
      } catch (err) {
        this.$Message.error("无法复制信息");
      }
    },
  },
  // 计算属性 类似于 data 概念
  computed: {},
  // 监控 data 中的数据变化
  watch: {},
  //过滤器
  filters: {},
  // 生命周期 - 创建完成（可以访问当前this 实例）
  created() {
  },
  // 生命周期 - 挂载完成（可以访问 DOM 元素）
  mounted() {},
}
</script>

<style scoped lang="less">
.log-content {
    display: flex;
    font-family:PingFang SC;
    color:#333333;
    font-size:20px;
    justify-content: center;
    text-align: center;
    .time {
        height: 28px;
        margin-bottom: 10px;
    }
    .xian {
         width: 92px;
         position: relative;
         height: 106px;
        .xians {
          position: absolute;
          left: 50%;
          top: 10px;
          transform: translateX(-50%);
        .by {
            width:20px;
            height:20px;
            border-radius: 100%;
            background:#537be6;
            z-index: 1;
        }
        .xc {
            width: 1px;
            height: 94px;
            background: #707070;
        }
        .yuan {
            width: 12px;
            height: 12px;
            border-radius: 100%;
            border: 1px solid #999;
            background: #fff;
            z-index: 1;
        }
        }
    }
    .bk {
        text-align: left;
        .bk-h {
            // max-height: calc(~"100vh - 68px");
            max-height: 100vh;
            overflow-y: auto;
            display: none;
            padding: 20px 0px 10px 10px;
            background: #5b5b5b;
            color: #f2f8eb;
            position: absolute;
            border-radius: 4px;
            z-index: 1;
        }
    }
     .bk:hover{
        .bk-h {
          display: block;
        }
     
     }
    /deep/.ivu-tooltip-rel {
      text-align: left;
    }
    /deep/ .ivu-tooltip-popper {
     padding-top: 0px;
    }
}
.left50 {
     position: relative;
     left: 50%;
     transform: translateX(-50%);
}
</style>