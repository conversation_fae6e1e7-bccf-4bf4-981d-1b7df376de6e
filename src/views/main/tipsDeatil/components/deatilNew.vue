<!-- 入口页 -->
<template>
  <div class="contents">
    <span class="edit">
      <span title="跳转信息详情页">
        <svg-icon
          @click.native="toDetails"
          icon-class="跳转详情链接"
          style="width: 20px; height: 20px;"
      /></span>

      <svg-icon
        @click.native="handleEdit"
        :icon-class="editFlag ? '提示单详情-保存' : '提示单详情-编辑'"
        style="width: 20px; height: 20px;"
    /></span>
    <p class="title">舆情提示单</p>
    <p class="nubmer">（编号:{{ dataItem.promptNum }}）</p>
    <div class="item-zong">
      <span class="item fw">发送单位</span>
      <span class="item" style="width: 307px; min-height: 60px;">{{
        dataItem.sendOrgName
      }}</span>
      <span class="item fw">发送时间</span>
      <span
        class="item"
        style="border-right: 1px solid #707070; width: 275px;"
        >{{ moment(dataItem.sendTime).format("YYYY-MM-DD HH:mm:ss") }}</span
      >
    </div>

    <div class="item-zong">
      <span class="item fw bt">接收人</span>
      <span class="item bt" style="width: 307px;">{{
        dataItem.sendUserName
      }}</span>
      <span class="item fw bt">联系电话</span>
      <span
        class="item bt"
        style="border-right: 1px solid #707070; width: 275px;"
        >{{ dataItem.sendPhone }}</span
      >
    </div>
    <div class="item-new bt">
      <span style="width: 127px;" class="fw">
        <span class="fw18">舆情线索</span>
      </span>
      <div style="text-align: left;" class="itm-msgAbstract" v-if="!editFlag">
        <span style="font-weight: 550; line-height: 28px; text-align: left;">{{
          handlejc(1, dataItem.msgAbstract) ? handlejc(1, dataItem.msgAbstract) : dataItem.msgAbstract
        }}</span>
        <span style="line-height: 28px; text-align: left;">{{
          handlejc(2, dataItem.msgAbstract)
            ? handlejc(2, dataItem.msgAbstract)
            : dataItem.msgAbstract
        }}</span>
        <p style="line-height: 28px; text-align: left;position: relative;">
          {{ dataItem.msgContentUrl }}
          <svg-icon v-if="dataItem.isMsgDel === 1" class="tips" icon-class="已删除"></svg-icon>
        </p>
      </div>
      <div
        style="text-align: left; padding: 10px 10px 0px 10px;"
        class="itm-msgAbstract"
        v-else
      >
        <Input
          v-model="dataItem.msgAbstract"
          type="textarea"
          :autosize="{ minRows: 1, maxRows: 8 }"
        />
        <p style="line-height: 28px; text-align: left;">
          {{ dataItem.msgContentUrl }}
        </p>
      </div>
    </div>

    <div class="item-new bt" style="min-height: 70px;">
      <span style="width: 127px;" class="fw">
        <span class="fw18" style="width: 54px;">舆情处置建议</span>
      </span>
      <div style="min-height: 70px;" class="itm-msgAbstract" v-if="!editFlag">
        <p style="line-height: 28px; text-align: left;">
          {{ dataItem.suggest }}
        </p>
      </div>
      <div
        style="min-height: 70px; padding: 10px 10px 0px 10px;"
        class="itm-msgAbstract"
        v-else
      >
        <Input v-model="dataItem.suggest" type="textarea" :rows="2" />
      </div>
    </div>

    <div class="item-new bt" style="min-height: 226px;">
      <span style="width: 127px;" class="fw">
        <span class="fw18">情况反馈</span>
      </span>
      <div style="min-height: 226px;" class="itm-msgAbstract">
        <p style="line-height: 28px; text-align: left;">
          {{ dataItem.backContent }}
        </p>
        <div class="flie_list">
          <div class="file_item" v-for="(item, index) in dataItem.fileList" :key="index" @click="downloadFile(item)"> 
            <svg-icon
              class="file_item_icon"
              :icon-class="checkFileType(item.fileName)"
              style="width: 20px;height: 20px;"
            />
            <span>{{ item.fileName }}</span>
          </div>
        </div>
        <!-- 提示单关联展示 -->
        <div v-if="dataItem.relationPromptList && dataItem.relationPromptList.length > 0" class="relation_tips" style="margin-top: 20px;">
          
          <div class="relation_list">
            <span class="name">关联提示单：</span>
            <span v-for="(item, index) in dataItem.relationPromptList" :key="index" class="relation_item">
              <span
                class="prompt_link"
                @click="openPromptDetail(item.promptNum)"
                :title="'点击查看提示单详情：' + item.promptNum"
              >
                {{ item.promptNum }}
              </span>
             
            </span>
          </div>
        </div>
      </div>
    </div>

    <p style="margin-top: 26px; font-size: 18px;">
      <span>单位：{{ dataItem.promptOrganName }}</span>
      <span>联系人：{{ dataItem.promptUserName }}</span>
      <span>电话：{{ dataItem.promptUserPhone }}</span>
    </p>
  </div>
</template>

<script>
import moment from "moment";
import fileDownload  from 'js-file-download';
export default {
  data() {
    return {
      editFlag: false,
    };
  },
  props: {
    dataItem: {
      default: () => {},
    },
  },
  //生命周期 - 创建完成（访问当前this实例）
  created() {},
  //方法所在
  methods: {
    toDetails() {
      console.log(this.dataItem);
      const { href } = this.$router.resolve({
        path: "/main/details",
        query: {
          msgKey: this.dataItem.msgId,
          situation: this.dataItem.msgSiution,
          sourcePath: this.$route.path,
          sourceName: this.$route.name,
        },
      });
      window.open(href, "_blank");
    },
    moment,
    handlejc(type, k) {
      let str = /[。？?!！]/;
      if (k) {
        let match = str.exec(k);
        return type == 1
          ? match
            ? k.slice(0, match.index)
            : ""
          : match
          ? k.slice(match.index + 1, k.length)
          : "";
      }
    },

    handleEdit() {
      if (!this.editFlag) {
        this.editFlag = true;
      } else {
        this.save();
      }
    },
    save() {
      let params = {
        msgAbstract: this.dataItem.msgAbstract,
        suggest: this.dataItem.suggest,
        promptMsgId: this.dataItem.promptMsgId,
      };
      this.$http
        .post("/prompt/updatePromptMsg", params, { emulateJSON: true })
        .then((res) => {
          if (res.body.status === 0) {
            this.editFlag = false;
          }
        });
    },
    downloadFile(v) {
      if(!v.fileUrl){
        return; 
      }
       let url = "/minIO/common/fileDownLoad";
        let formData = new FormData();
        formData.append("fileName", v.fileName);
        formData.append("fileUrl", v.fileUrl);
        this.$http.post(url, formData,{ responseType: "blob" }).then((res) => {
          fileDownload(res.body, v.fileName);
        });
    },
    checkFileType(name) {
      const fileName = name.toLowerCase();
      // 文件类型判断
      if (fileName.endsWith('.doc') || fileName.endsWith('.docx')) {
        return 'WORD1';
      } else if (fileName.endsWith('.xls') || fileName.endsWith('.xlsx')) {
        return 'ECEL1';
      } else if (fileName.endsWith('.pdf')) {
        return 'PDF1';
      } else if (fileName.endsWith('.zip') || fileName.endsWith('.rar') || fileName.endsWith('.7z')) {
        return '压缩包';
      } else {
        return '附件';
      }
    },
    // 打开提示单详情页面
    openPromptDetail(promptNum) {
      const { href } = this.$router.resolve({
        path: "/main/tipsDeatil",
        query: {
          promptNum: promptNum
        },
      });
      window.open(href, "_blank");
    },
  },
  //生命周期 - 挂载完成（访问DOM元素）
  mounted() {},
};
</script>
<style lang="less" scoped>
/* @import url(); 引入css类 */
.contents {
  text-align: center;
  font-family: PingFang SC;
  color: #333333;
  position: relative;
  .edit {
    position: absolute;
    top: -15px;
    right: 10px;
    cursor: pointer;
  }
  .title {
    height: 38px;
    text-align: center;
    margin-top: 29px;
    font-family: PingFang SC;
    font-weight: 600;
    color: #333333;
    font-size: 27px;
  }
  .nubmer {
    font-size: 23px;
    margin-bottom: 10px;
  }
  .item-zong {
    display: flex;
    font-size: 18px;
    justify-content: center;
    align-items: center;
    .item {
      width: 128px;
      min-height: 58px;
      line-height: 58px;
      border: 1px solid #707070;
      border-right: 0px;
    }
  }
  .item-new {
    margin: auto;
    display: flex;
    width: 838px;
    min-height: 260px;
    //  align-items: center;
    border: 1px solid #707070;
  }
}

.fw {
  font-weight: 600;
}
.fw18 {
  font-size: 18px;
  font-family: PingFang SC;
  display: inline-block;
  width: 30px;
  position: relative;
  top: 50%;
  transform: translateY(-50%);
}
.bt {
  border-top: 0px !important;
}
.itm-msgAbstract {
  width: 710px;
  padding: 20px 0px 0px 10px;
  border-left: 1px solid #707070;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  .flie_list{
    margin-top: 20px;
    display: flex;
    flex-wrap: wrap;
    column-gap: 20px;
    row-gap: 10px;
    justify-content: flex-end;
    align-items: center;
    padding: 10px 10px 5px 10px;
  }
  .file_item{
    cursor: pointer;
    display: flex;
    align-items: center;
    span{
      font-size: 12px;
      margin-left: 5px;
    }
  }
}
.tips{
  transform: rotate(-15deg);
  transform-origin: center;
  width: 40px;
  height: 40px;
  margin-left: 5px;
  vertical-align: middle;
}

// 提示单关联样式
.relation_tips {
  padding: 10px;

}

.relation_list {
  line-height: 24px;
  display: flex;
  font-size:16px;
  .name{
    color:#707070
  }
}

.relation_item {
  margin-right: 10px;
}

.prompt_link {
  color: #1890ff;
  cursor: pointer;
  text-decoration: underline;

  &:hover {
    color: #40a9ff;
    text-decoration: underline;
  }
}
</style>
