<!-- 入口页 -->
<template>
    <div style="width: 100%; padding: 0px 31px 0px 20px">
      <div
        class="lists"
        v-for="(item, index) in dataLists"
        :key="item.id"
        :style="index == dataLists.length ? 'margin-bottom:0px;' : ''"
      >
        <div class="lists-main">
          <div class="lists-index">{{ index + 1 }}</div>
          <div class="lists-content">
            <div class="lists-title" :title="item.title" @click="goDetail(item)">{{ item.title }}</div>
            <div class="lists-abstract" @click="goDetail(item)" v-html="item.caseAbstract"></div>
            <div class="lists-meta">
              <span>发布时间：{{ formatTime(item.createTime) }}</span>
              <span class="lists-ren">发布人：{{ item.createUserName }}</span>
              <span class="lists-class">{{ item.caseClass }}</span>
            </div>
          </div>
          <div class="lists-actions">
            <div class="btns" @click="editCase(item)">
              <svg-icon icon-class="重点提示-生成提示单" style="width: 11.13px; height: 10.3px; margin-right: 2.76px" />修改
            </div>
            <div class="btns" @click="del(item)">
              <svg-icon icon-class="列表-删除" style="width: 11.13px; height: 10.3px; margin-right: 2.76px" />删除
            </div>
          </div>
        </div>
      </div>
    </div>
  </template>
  
  <script>
  import moment from "moment";
  import axios from "axios";
  export default {
    data() {
      return {
        dataLists: [],
        delModalVisible: false,
        delId: null,
        detail: {}
      };
    },
    watch: {
      dataList: {
        handler(newVal) {
          this.dataLists = newVal;
        },
        deep: true
      }
    },
    props: {
      dataList: {
        default: () => [],
      }
    },
    //生命周期 - 创建完成（访问当前this实例）
    created() {
      const id = this.$route.query.id;
      this.$http.get('/case/findCaseById', { params: { id } }).then(res => {
        if (res.data && res.data.status === 0) {
          this.detail = res.data.data;
        }
      });
    },
    //方法所在
    methods: {
      formatTime(ts) {
        if (!ts) return '';
        return moment(ts).format('YYYY-MM-DD');
      },
      editCase(item) {
        // 跳转新建页并带入内容
        const params = encodeURIComponent(JSON.stringify(item));
        window.open(`/main/caseLibraryDeatil?id=${item.id}`);
      },
      del(d) {
        console.log(d)
        this.$emit("del",'dan', d.id);
      },
      goDetail(item) {
        window.open(`/main/caseLibraryDetailView?id=${item.id}`);
      }
    },
    //生命周期 - 挂载完成（访问DOM元素）
    mounted() {
  
    },
  };
  </script>
  <style lang="less" scoped>
  /* @import url(); 引入css类 */
  .lists {
    display: flex;
    flex-direction: column;
    width: 100%;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.16);
    margin-bottom: 12px;
    padding: 14px 24px 18px 10px;
    position: relative;
    margin-top: 12px;
    .lists-main {
      display: flex;
      align-items: flex-start;
    }
    .lists-index {
      width: 32px;
      font-size: 16px;
      color: #222;
      font-weight: 600;
      flex-shrink: 0;
      text-align: left;
      margin-right: 16px;
      left:5px;
      position: relative;
    }
    .lists-content {
      flex: 1;
      min-width: 0;
      .lists-title {
        font-size: 16px;
        font-weight: 600;
        color: #222;
        margin-bottom: 6px;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        cursor: pointer;
      }
      .lists-abstract {
        font-size: 16px;
        color: #444;
        margin-bottom: 8px;
        line-height: 1.4em;
        max-height: 2.8em;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }
      .lists-meta {
        font-size: 14px;
        color: #888;
        display: flex;
        gap: 24px;
        .lists-ren {
          color: #222;
          font-weight: 500;
        }
        .lists-class {
          color: #3987ec;
          font-weight: 500;
        }
      }
    }
    .lists-actions {
      display: flex;
      flex-direction: row;
      align-items: flex-end;
      gap: 10px;
      margin-left: 24px;
      position: absolute;
      bottom:5px;
      right:10px;

      .btns {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 28px;
        width: 70px;
        background: #e6f7ff;
        border: 1px solid #5585ec;
        border-radius: 4px;
        font-size: 14px;
        color: #666666;
        cursor: pointer;
        margin-bottom: 4px;
      }
    }
  }
  .foots {
    display: inline-block;
    width: 70px;
    height: 34px;
    line-height: 34px;
    text-align: center;
    background: #5585ec;
    border-radius: 4px;
    color: #fff;
    font-size: 14px;
    cursor: pointer;
    margin: 0 10px;
    &.cancel {
      background: #999;
    }
  }
  </style>
  