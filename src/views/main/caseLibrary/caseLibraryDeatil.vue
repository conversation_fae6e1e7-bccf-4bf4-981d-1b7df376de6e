<template>
  <div class="case-detail-wrapper">
    <div class="case-detail-title">{{ isEdit ? '编辑案例' : '新建案例' }}</div>
    <Form ref="form" :model="form" :rules="rules" label-width="110px" class="case-detail-form">
      <FormItem label="案例分类：" prop="caseClass">
        <Select v-model="form.caseClass" placeholder="请选择案例分类">
          <Option v-for="item in caseClassOptions" :key="item" :value="item">{{ item }}</Option>
        </Select>
      </FormItem>
      <FormItem label="案例标题：" prop="title">
        <Input v-model="form.title" placeholder="请输入案例标题" />
      </FormItem>
      <FormItem label="案例摘要：" prop="caseAbstract">
        <RichTextEditor
          v-model="form.caseAbstract"
          placeholder="请输入案例摘要"
          height="150px"
        />
      </FormItem>
      <FormItem label="舆情内容：" prop="content">
        <RichTextEditor
          v-model="form.content"
          placeholder="请输入舆情内容"
          height="200px"
        />
      </FormItem>
      <FormItem label="事件分析：" prop="eventAnalysis">
        <RichTextEditor
          v-model="form.eventAnalysis"
          placeholder="请输入事件分析"
          height="200px"
        />
      </FormItem>
      <FormItem label="文件上传：">
        <Upload
          ref="upload"
          :action="uploadUrl"
          :data="{ type: '20' }"
          :before-upload="handleBeforeUpload"
          :on-success="handleUploadSuccess"
          :on-remove="handleRemove"
          :file-list="fileList"
          :show-upload-list="false"
          :multiple="true"
          :max-size="maxFileSize"
          :format="fileFormats"
          :headers="uploadHeaders"
          >
          <Button icon="ios-cloud-upload-outline">上传文件</Button>
        </Upload>
        <div class="file-list-custom">
          <div v-for="(file, idx) in fileList" :key="file.uid || file.name" class="file-row">
            <Icon :type="getFileIcon(file.name)" class="file-icon" />
            <a
              class="file-link"
              :href="file.url"
              :download="file.name"
              target="_blank"
              v-if="file.url"
            >{{ file.name }}</a>
            <span v-else class="file-link-disabled">{{ file.name }}</span>
            <Icon type="md-close-circle" class="file-remove" @click="removeFile(idx)" title="删除" />
          </div>
        </div>
        <div class="file-tip">支持格式：doc、docx、xls、xlsx、pdf，文件大小限制：200M</div>
      </FormItem>
      <FormItem style="text-align: center;">
        <Button type="primary" @click="handleSubmit">确定</Button>
        <Button style="margin-left: 24px;" @click="handleTempSave">暂存并关闭</Button>
      </FormItem>
    </Form>
  </div>
</template>

<script>
export default {
  name: 'CaseLibraryDeatil',
  data() {
    return {
      isEdit: false,
      form: {
        caseClass: '',
        title: '',
        caseAbstract: '',
        content: '',
        eventAnalysis: '',
        files: '', // 逗号分隔的文件id
      },
      rules: {
        caseClass: [ { required: true, message: '请选择案例分类', trigger: 'change' } ],
        title: [ { required: true, message: '请输入案例标题', trigger: 'blur' } ],
        caseAbstract: [ { required: true, message: '请输入案例摘要', trigger: 'blur' } ],
        content: [ { required: true, message: '请输入舆情内容', trigger: 'blur' } ],
        eventAnalysis: [ { required: true, message: '请输入事件分析', trigger: 'blur' } ],
      },
      caseClassOptions: [
        '政治生态','意识形态','经济发展','社会民生','文化旅游','学校教育','医疗卫生','农业农村','城建房产','交通物流','生态环保','民族宗教','信访维权','涉法涉诉','突发事件'
      ],
      fileList: [], // 上传组件文件列表
      uploadUrl: 'api/minIO/common/uploadFiles',
      maxFileSize: 200 * 1024 * 1024, // 200M
      fileFormats: ['doc','docx','xls','xlsx','pdf'],
      uploadHeaders: {
              token: localStorage.getItem('tokens') ? `${localStorage.getItem('tokens')}` : undefined,
      },
      tempKey: 'caseLibraryDeatil_temp',
      editId: null,
    };
  },
  created() {
    // 判断是否为编辑
    const id = this.$route.query.id;
    if (id) {
      this.isEdit = true;
      this.editId = id;
      this.getDetail(id);
    } else {
      // 新建时，尝试读取暂存
      this.loadTemp();
      // 优先级1：url传参 caseClass
      const caseClassParam = this.$route.query.caseClass;
      if (caseClassParam && this.caseClassOptions.includes(caseClassParam)) {
        this.form.caseClass = caseClassParam;
      } else {
        // 优先级2：fromClass
        const fromClass = this.$route.query.fromClass;
        if (fromClass && this.caseClassOptions.includes(fromClass)) {
          this.form.caseClass = fromClass === '全部案例' ? '' : fromClass;
        }
      }
    }
  },
  methods: {
    // 获取详情
    async getDetail(id) {
      try {
        const res = await this.$http.get('/case/findCaseById', { params: { id } });
        if (res.data && res.data.status === 0) {
          const d = res.data.data;
          this.form = {
            caseClass: d.caseClass || '',
            title: d.title || '',
            caseAbstract: d.caseAbstract || '',
            content: d.content || '',
            eventAnalysis: d.eventAnalysis || '',
            files: d.files || '',
          };
          // 回显文件
          if (d.fileList && Array.isArray(d.fileList)) {
            this.fileList = d.fileList.map(f => ({
              name: f.fileName,
              url: f.url,
              response: { data: { id: f.id } },
              status: 'done',
              uid: f.id
            }));
          }
        } else {
          this.$Message.error(res.data?.message || '获取详情失败');
        }
      } catch (e) {
        this.$Message.error('网络异常，获取详情失败');
      }
    },
    // 文件上传前校验
    handleBeforeUpload(file) {
      const ext = file.name.split('.').pop().toLowerCase();
      if (!this.fileFormats.includes(ext)) {
        this.$Message.error('不支持的文件格式');
        return false;
      }
      if (file.size > this.maxFileSize) {
        this.$Message.error('文件大小不能超过200M');
        return false;
      }
      return true;
    },
    // 上传成功
    handleUploadSuccess(res, file, fileList) {
      if (res && res.status === 0 && res.data) {
        // 适配后端返回结构
        file.response = { data: res.data };
        file.uid = res.data.fileId;
        file.name = res.data.fileName;
        // 拼接下载接口前缀，假设接口为 /api/minIO/common/download?fileUrl=
        file.url = '/api/minIO/common/download?fileUrl=' + encodeURIComponent(res.data.fileUrl);
        this.fileList = fileList;
      } else {
        this.$Message.error('文件上传失败');
      }
    },
    // 删除文件
    handleRemove(file, fileList) {
      this.fileList = fileList;
    },
    // 暂存并关闭
    handleTempSave() {
      localStorage.setItem(this.tempKey, JSON.stringify(this.form));
      this.$Message.success('已暂存');
      window.close();
    },
    // 读取暂存
    loadTemp() {
      const temp = localStorage.getItem(this.tempKey);
      if (temp) {
        this.form = Object.assign(this.form, JSON.parse(temp));
      }
    },
    // 提交
    handleSubmit() {
      this.$refs.form.validate(async valid => {
        if (!valid) return;
        // 整理文件id
        const fileIds = this.fileList.map(f => f.response?.data?.fileId).filter(Boolean);
        this.form.files = fileIds.join(',');
        // 编辑时带id
        if (this.isEdit && this.editId) {
          this.form.id = this.editId;
        }
        try {
          const res = await this.$http.post('/case/insertOrUpdateCase', this.form);
          if (res.data && res.data.status === 0) {
            this.$Message.success('保存成功');
            localStorage.removeItem(this.tempKey);
            window.opener && window.opener.location.reload();
            // window.close();
          } else {
            this.$Message.error(res.data?.message || '保存失败');
          }
        } catch (e) {
          this.$Message.error('网络异常，保存失败');
        }
      });
    },
    // 获取文件图标
    getFileIcon(name) {
      const ext = name.split('.').pop().toLowerCase();
      if (["doc", "docx"].includes(ext)) return "ios-document";
      if (["xls", "xlsx"].includes(ext)) return "logo-buffer";
      if (["pdf"].includes(ext)) return "ios-paper";
      return "ios-attach";
    },
    // 删除文件（自定义）
    removeFile(idx) {
      this.fileList.splice(idx, 1);
    },
  }
};
</script>

<style lang="less" scoped>
/deep/.ivu-input,/deep/.ivu-select-placeholder,/deep/.ivu-select-selected-value{
  font-size: 14px!important;
}
/deep/.ivu-btn{
  font-size: 14px!important;
  height: 36px!important;
  padding: 0 20px!important;
}
.case-detail-wrapper {
  background: #fff;
  border-radius: 8px;
  min-height: ~'calc(100% - 40px)';
  max-width: 100%;
  margin: 20px auto 0 20px;
  padding: 32px 48px 32px 48px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}
.case-detail-title {
  font-size: 22px;
  font-weight: 600;
  margin-bottom: 32px;
  text-align: left;
}
.case-detail-form {
  .ivu-form-item {
    display: flex;
    margin-bottom: 28px;
    flex-direction: row;
    /deep/.ivu-form-item-label{
      font-size: 16px;
      min-width: 100px;
      text-align: right;
    }
    /deep/.ivu-form-item-content{
      flex: 1;
    }
  }
  .file-tip {
    color: #888;
    font-size: 13px;
    margin-top: 8px;
  }
}
.file-list-custom {
  margin-top: 8px;
  margin-bottom: 8px;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 20px;
  font-size: 16px;
  .file-row {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    .file-icon {
      margin-right: 8px;
    }
    .file-link {
      margin-right: 8px;
      color: #1890ff;
      text-decoration: none;
      &:hover {
        text-decoration: underline;
      }
    }
    .file-link-disabled {
      color: #888;
    }
    .file-remove {
      margin-left: 8px;
      cursor: pointer;
    }
  }
}
</style> 