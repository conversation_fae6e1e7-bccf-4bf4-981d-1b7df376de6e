<template>
  <div class="case-detail-view-wrapper">
    <h2 class="case-title">{{ detail.title }}</h2>
    <div class="case-meta">
      <span>发布时间：{{ formatTime(detail.createTime) }}</span>
      <span class="lists-ren">发布人：{{ detail.createUserName }}</span>
      <span class="case-class">{{ detail.caseClass }}</span>
    </div>
    <div class="case-section">
      <b>案例摘要：</b>
      <div class="case-content rich-content" v-html="detail.caseAbstract"></div>
    </div>
    <div class="case-section">
      <b>舆情内容：</b>
      <div class="case-content rich-content" v-html="detail.content"></div>
    </div>
    <div class="case-section">
      <b>事件分析：</b>
      <div class="case-content rich-content" v-html="detail.eventAnalysis"></div>
    </div>
    <div class="case-section" v-if="detail.fileList && detail.fileList.length">
      <b>文件：</b>
      <div class="file-list-custom">
        <div v-for="file in detail.fileList" :key="file.fileId" class="file-row">
          <Icon :type="getFileIcon(file.fileName)" class="file-icon" />
          <a
            class="file-link"
            @click="downloadFile(file)"
            href="javascript:void(0)"
          >{{ file.fileName }}</a>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import fileDownload from 'js-file-download';

export default {
  name: 'CaseLibraryDetailView',
  data() {
    return {
      detail: {}
    }
  },
  created() {
    const id = this.$route.query.id;
    this.$http.get('/case/findCaseById', { params: { id } }).then(res => {
      if (res.data && res.data.status === 0) {
        this.detail = res.data.data;
        this.historyLog('案例库', '信息详情页/'+this.detail.title, this.detail.id);
      }
    });
  },
  methods: {
    formatTime(ts) {
      if (!ts) return '';
      const d = new Date(ts);
      return d.getFullYear() + '-' + String(d.getMonth() + 1).padStart(2, '0') + '-' + String(d.getDate()).padStart(2, '0');
    },
    // 下载文件
    downloadFile(file) {
      if (!file.fileUrl) {
        this.$Message.error('文件地址不存在');
        return;
      }

      let url = "/minIO/common/fileDownLoad";
      let formData = new FormData();
      formData.append("fileName", file.fileName);
      formData.append("fileUrl", file.fileUrl);

      this.$http.post(url, formData, { responseType: "blob" }).then((res) => {
        fileDownload(res.body, file.fileName);
      }).catch((error) => {
        console.error('文件下载失败:', error);
        this.$Message.error('文件下载失败');
      });
    },
    getFileIcon(name) {
      const ext = name.split('.').pop().toLowerCase();
      if (["doc", "docx"].includes(ext)) return "ios-document";
      if (["xls", "xlsx"].includes(ext)) return "logo-buffer";
      if (["pdf"].includes(ext)) return "ios-paper";
      return "ios-attach";
    }
  }
}
</script>

<style lang="less" scoped>
/deep/.frame-content{
  margin:0;padding:0;
}
.case-detail-view-wrapper {
  background: #fff;
  border-radius: 8px;
  min-height: ~'calc(100% - 40px)';
  max-width: 100%;
  box-sizing: border-box;
  margin: 20px auto 20px auto;
  padding: 32px 48px 32px 48px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  position: relative;
}
.case-title {
  font-size: 22px;
  font-weight: 600;
  margin-bottom: 18px;
  text-align: left;
}
.case-meta {
  font-size: 14px;
  color: #888;
  margin-bottom: 18px;
  display: flex;
  gap: 24px;
  .lists-ren {
          color: #222;
          font-weight: 500;
        }
  .case-class {
    color: #3987ec;
    font-weight: 500;
  }
}
.case-section {
  margin-bottom: 25px;
  b {
    font-weight: 500;
    color: #222;
    padding-left:15px;
    position: relative;
    margin-bottom: 20px;
    font-size: 16px;
    line-height: 16px;
    &::before{
      content: '';
      position: absolute;
      display: block;
      left: 0;
      top: 3px;
      bottom:3px;
      width:3px;

      background: #1890ff;
    }
  }
  .case-content {
    margin-top: 4px;
    color: #222;
    font-size: 16px;
    line-height: 1.7;
    white-space: pre-line;

    &.rich-content {
      // 富文本内容样式
      /deep/ p {
        margin: 8px 0;
      }

      /deep/ h1, /deep/ h2, /deep/ h3, /deep/ h4, /deep/ h5, /deep/ h6 {
        margin: 12px 0 8px 0;
        font-weight: 600;
      }

      /deep/ ul, /deep/ ol {
        margin: 8px 0;
        padding-left: 20px;
      }

      /deep/ blockquote {
        margin: 8px 0;
        padding: 8px 16px;
        background-color: #f5f5f5;
        border-left: 4px solid #ddd;
      }

      /deep/ code {
        background-color: #f5f5f5;
        padding: 2px 4px;
        border-radius: 3px;
        font-family: 'Courier New', monospace;
      }

      /deep/ pre {
        background-color: #f5f5f5;
        padding: 12px;
        border-radius: 4px;
        overflow-x: auto;
        margin: 8px 0;
      }

      /deep/ a {
        color: #1890ff;
        text-decoration: none;

        &:hover {
          text-decoration: underline;
        }
      }
    }
  }
}
.file-list-custom {
  display: flex;
  margin-top: 8px;
  flex-direction: row;
  flex-wrap: wrap;
  font-size: 16px;
  .file-row {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    .file-icon {
      margin-right: 8px;
    }
    .file-link {
      margin-right: 8px;
      color: #1890ff;
      text-decoration: none;
      &:hover {
        text-decoration: underline;
      }
    }
  }
}
</style> 