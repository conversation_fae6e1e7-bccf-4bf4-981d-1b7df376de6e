<!-- 主体 -->
<template>
  <div class="content">
    <div class="nav">
      <div class="name">监测系统</div>
      <div class="nav-content">
        <div class="nav-item">
          <div
          
              class="nav-list"
              @click="goToPublicOpinionOverviewNew"
            >
              <span>
                <svg-icon icon-class="nav-首页" />
                首页
              </span>
            </div>
          <Poptip
            v-for="val in navList"
            v-show="val.svgIcon"
            :key="val.path"
            ref="poptip"
            :disabled="!subclassList.includes(val.name)"
            placement="right"
            @on-popper-hide="popperHide"
          >
            <div
              :class="NavId.indexOf(val.path) !== -1 ? 'home-Page' : ''"
              class="nav-list"
              @click="goPath(val)"
            >
              <span>
                <svg-icon :icon-class="'nav-' + val.svgIcon" />
                {{ val.name }}
              </span>
            </div>
            <div slot="content" class="popper">
              <div class="childrenBox">
                <div
                  v-for="v in val.children"
                  v-show="v.show !== false"
                  :key="v.path"
                  :style="
                    childrenId == v.path ? 'background:#6d7dff;color:#fff;' : ''
                  "
                  class="item"
                  @click="goPath(v, 2, val)"
                >
                  {{ v.name }}
                </div>
              </div>
            </div>
          </Poptip>
        </div>
        <div class="login-out">
          <div @click="goToPersonalCenter">
            <svg-icon
              icon-class="哨点-人员"
              style="width: 45px; height: 45px; margin-bottom: 7px;"
            />
          </div>
          <div>
            <b>{{ userName ? userName : "模拟用户" }}</b>
          </div>
          <div>
            {{ roleName ? roleName : "模拟用户角色" }}

            <Poptip
              confirm
              title="确定要退出登录吗？"
              transfer
              @on-ok="loginOut()"
            >
              <svg-icon
                icon-class="哨点-退出"
                style="
                  width: 16px;
                  height: 14px;
                  margin-left: 5px;
                  cursor: pointer;
                "
              />
            </Poptip>
          </div>
        </div>
      </div>
    </div>
    <div class="frame">
      <div class="frame-content">
        <RouterView></RouterView>
      </div>
      <div
        class="frame-path"
        style="padding-top: 20px; padding-bottom: 20px;"
        :class="{ pathUnfold: toolShow }"
        @mouseenter="mouseenter"
        @mouseleave="mouseleave"
      >
        <svg-icon
          class="assistant cp"
          icon-class="avatar-ai"
          @click.native="show = true"
        />
        <span
          v-for="v in toolboxList"
          :key="v.id"
          :title="v.name"
          @click="goTool(v)"
        >
          <svg-icon
            :icon-class="v.name"
            style="
              width: 39px;
              height: 39px;
              cursor: pointer;
              margin-top: 20px;
            "
          />
        </span>
      </div>
    </div>
    <div
      class="tool"
      :class="{ unfold: toolShow }"
      @mouseenter="mouseenter"
      @mouseleave="mouseleave"
    >
      <div class="toolBtn">
        <svg-icon icon-class="工具箱" />
      </div>
    </div>

    <Chat :show="show" @close="closeChat" />
    <NewsPush />
    <SmsPush />
    <HotRankPush />

    <form id="ticketmessage" action="" method="post" target="_blank">
      <input id="fortheticketvalue" name="token" type="hidden" value="" />
      <input id="to" name="to" type="hidden" value="" />
    </form>
  </div>
</template>

<script>
import Chat from "@/components/chat";
import NewsPush from "@/components/NewsPush";
import SmsPush from "@/components/smsSending";
import HotRankPush from "@/components/hotRankPush";
import $ from "jquery";

export default {
  data() {
    return {
      show: false,
      NavId: this.$route.path, //选中id
      childrenId: this.$route.path,
      navList: [],
      toolShow: false,
      pathTimeOut: null,
      subclassList: [
        "知识管理",
        "评价考核",
        "网情要报",
        "配置管理",
        "全息档案",
        "专题数据",
      ],
      toolboxList: [
        {
          id: 0,
          path: "http://************:8002/webim/pc/#/chat",
          name: "即时通讯",
        },
        {
          id: 5,
          path: "http://************/front/api/v1/TRS/SSOLogin",
          sysNo: "sys_wjcs",
          name: "跨网传输",
        },
        { id: 1, path: "", name: "ai报告" },
        { id: 2, path: "/main/yqCalendar", name: "舆情日历" },
        { id: 3, path: "/main/dutylog", name: "值班日志" },
        { id: 4, path: "/main/dutyTable", name: "值班表" },
        { id: 6, path: "/main/comprehensiveSearch", name: "精确搜索" },
      ],
      userName: localStorage.getItem("userName"),
      roleName: localStorage.getItem("roleName"),
    };
  },
  components: { Chat, NewsPush, SmsPush, HotRankPush },
  created() {
    this.navList = this.$router.options.routes[1].children;
    console.log(this.navList);
  },
  methods: {
    goToPersonalCenter() {
      this.$router.push("/main/PersonalCenter");
    },
    mouseleave() {
      this.pathTimeOut = setTimeout(() => {
        this.toolShow = false;
      }, 1000);
    },
    mouseenter() {
      this.toolShow = true;
      if (this.toolShow) {
        clearTimeout(this.pathTimeOut);
        this.pathTimeOut = null;
      }
    },
    closeChat() {
      this.show = false;
    },
    goPath(d, type, parent) {
      if (!this.subclassList.includes(d.name)) {
        this.$router.push(d.path);
        this.childrenId = "";
        this.visible = false;
        if (this.$refs.poptip) {
          this.$refs.poptip.forEach((i) => {
            i.handleClose();
          });
        }
      }
      if (this.subclassList.includes(d.name)) {
        document.title = d.name;
      }
      if (type && parent) {
        this.NavId = parent.path;
        this.childrenId = d.path;
      } else {
        this.NavId = d.path;
      }
    },
    ssoGoPath(item) {
      if (item.windowOpen) {
        window.open(item.path);
        return;
      }
      if (!item.path) {
        return;
      }

      let token = localStorage.getItem("tokens");
      $("#fortheticketvalue").val(token);
      if (item.to) $("#to").val(item.to);
      $("#ticketmessage").attr("action", item.path);
      $("#ticketmessage").submit();
      console.log("单点登录:" + this.formInline.token);
    },
    popperHide() {
      if (!this.childrenId) {
        this.NavId = this.$route.path;
      }
    },
    // 工具箱
    goTool(item) {
      let path = item.path;
      let id = item.id;
      // 点击小工具 取消左侧选中
      if (path.includes("http")) {
        let params = {
          iphone: localStorage.getItem("iphone"),
        };
        if (id == 5) {
          this.getLog('工具栏/跳转跨网传输','跳转');
          this.ssoGoPath(item);
          return;
        }
        this.$http.get("/common/message", { params }).then((res) => {
          console.log(res);
          if (res.body.data) {
            window.open(res.body.data, "_blank");
            this.getLog('工具栏/跳转即时通讯','跳转');
          }
        });
      } else {
        this.NavId = "";
        this.$router.push(path);
      }
    },
    loginOut() {
      this.$http.get("/login/logout").then((res) => {
        if (res.body.status === 0) {
          localStorage.removeItem("userId");
          localStorage.removeItem("userName");
          localStorage.removeItem("organName");
          localStorage.removeItem("organId");
          localStorage.removeItem("organName");
          localStorage.removeItem("userAccount");
          localStorage.removeItem("resources");
          localStorage.removeItem("sysNos");
          localStorage.removeItem("iphone");
          localStorage.removeItem("roleName");
          localStorage.removeItem("tokens");
          gl.loginU = {};
          this.$Message.success("退出登录成功！");
          window.location.href = process.env.HOME_WEB;
        } else {
          this.$Message.error({
            content: "退出失败",
            duration: 3,
            closable: true,
          });
        }
      });
    },
    goToPublicOpinionOverviewNew() {
      window.open("/publicOpinionOverviewNew", "_blank");
    },
  },
  watch: {
    $route() {
      document.title = this.$route.name;
    },
  },
  beforeUnmount() {
    this.clearTimer();
  },
  mounted() {
    document.title = this.$route.name;
  },
};
</script>
<style lang="less" scoped>
.tool {
  position: fixed;
  right: 0;
  bottom: 50px;
  width: 50px;
  opacity: 0.6;
  padding: 5px 10px;
  border-radius: 20px 0 0 20px;
  background-color: #547de7;
  transition: width 0.5s ease, opacity 0.5s ease;
  .toolBtn {
    cursor: pointer;
  }
}
.unfold {
  width: 100px;
  opacity: 1;
}
.frame-path {
  position: fixed;
  width: 54px;
  right: -60px;
  bottom: 100px;
  transition: transform 0.5s ease;
  background: #fff;
  border-radius: 8px 0px 0px 8px;
  box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.16);
  padding-left: 8px;
}
.pathUnfold {
  transform: translateX(-60px); /* 横向位移 */
}
.content {
  width: 1920px;
  height: 100%;
  display: flex;
  font-family: PingFang SC;
  font-size: 16px;
  overflow: hidden;
  background: #fbf7fa;

  .nav {
    width: 140px;
    // height: 100%;
    color: #ffffff;
    background: linear-gradient(180deg, #5585ec 0%, #4e57d2 100%);
    border-radius: 0 20px 20px 0;
    margin-right: 20px;

    .nav-content {
      height: calc(~"100% - 100px");
      display: flex;
      justify-content: space-between;
      flex-direction: column;
      padding-bottom: 30px;

      .nav-item {
        display: flex;
        justify-content: space-between;
        flex-direction: column;

        .nav-list {
          width: 140px;
          margin-bottom: 10px;
          cursor: pointer;
          font-size: 14px;
          padding: 4px 0 6px 17px;
          position: relative;
          transition: all 0.3s;
        }

        .nav-list:hover {
          background: #6da0ee;
          border-radius: 12px;
        }

        .popper {
          .childrenBox {
            .item {
              color: #333;
              text-align: center;
              line-height: 35px;
              border-bottom: 1px solid #eee;
              font-size: 16px;
              cursor: pointer;
            }

            .item:last-child {
              border-bottom: 0;
            }

            .item:hover {
              color: #5585ec;
            }
          }
        }
      }

      .login-out {
        font-size: 12px;
        display: flex;
        align-items: center;
        flex-direction: column;
        margin-left: -10px;
      }
    }

    .name {
      text-shadow: 0 2px 0 rgba(0, 46, 132, 0.32);
      font-weight: 600;
      font-size: 18px;
      text-align: center;
      margin: 15px 16px 28px 16px;
    }

    .home-Page {
      background: #6da0ee;
      border-radius: 12px;
      font-size: 16px !important;
    }
  }

  .frame {
    // width: calc(~"100% - 160px");
    flex: 1;
    position: relative;
    overflow-y: auto;

    .frame-content {
      width: 100%;
      height: 100%;
    }
  }
}

.assistant {
  // position: fixed;
  right: 0;
  width: 39px;
  height: 39px;
  bottom: 400px;
  z-index: 900;
}
</style>
