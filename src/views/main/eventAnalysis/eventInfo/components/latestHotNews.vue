<template>
  <div class="LatestNews">
    <div class="select">
      <Select v-model="situationId" style="width: 150px;">
        <Option v-for="(v, k) in situation" :value="k" :key="k">{{ v }}</Option>
      </Select>
    </div>
    <Spin v-show="loading" fix>
      <Icon type="ios-loading" size="18" class="demo-spin-icon-load"></Icon>
      <div>Loading</div>
    </Spin>
    <no-data v-show="dataList.length === 0 && !loading" />
    <div class="listBox">
      <div class="item" v-for="(item, index) in dataList" :key="item.mkey">
        <template v-if="item.situation === 10">
          <div class="weiboTitle ellipsis-2 cp" @click="toDetails(item)">
            <span class="num">{{ index + 1 }}</span>
            {{ removeROrN(item.msgTitle) }}
          </div>
        </template>
        <template v-else>
          <div class="title ellipsis cp" @click="toDetails(item)">
            <span class="num">{{ index + 1 }}</span
            >{{ removeROrN(item.msgTitle) }}
          </div>
        </template>
        <div class="remark">
          <span>{{ item.msgPublishTime }}</span>
          <span
            class="source ellipsis"
            :title="
              (situation[item.situation] && item.situation != 600 && item.situation != 80 ? situation[item.situation] : '') +
              (item.msgSiteName ? item.situation == 600 || item.situation == 80 ? item.msgSiteName : '-' + item.msgSiteName : '') +
              (item.uname ? '-' + item.uname : '')
            "
            ><PlatformIcon :id="item.situation" />
            {{
              (situation[item.situation] && item.situation != 600 && item.situation != 80 ? situation[item.situation] : '') +
              (item.msgSiteName ? item.situation == 600 || item.situation == 80 ? item.msgSiteName : '-' + item.msgSiteName : '') +
              (item.uname ? '-' + item.uname : '')
            }}
          </span>
          <span :title="item.sameMsgCount || 0">
            相似文章数:{{ item.sameMsgCount || 0 }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
// 这里可以导入其他文件（比如：组件，工具 js，第三方插件 js，json 文件，图片文件等等）
// 例如：import  《组件名称》  from '《组件路径》 ';
import moment from "moment";
import PlatformIcon from "@/components/platformIcon";
// import situation from "@/assets/json/situations.json";
const situation = {
  0: "全部平台",
  30: "媒体网站",
  31: "客户端",
  10: "新浪微博",
  20: "微信公号",
  80: "小红书",
  60: "论坛贴吧",
  // 62: "贴吧",
  600: "今日头条",
  199: "短视频",
  // 200: "短视频",
  // 230: "自媒体",
};
export default {
  data() {
    // 这里存放数据
    return {
      situationId: "0",
      situation,
      dataList: [],
      loading: false,
    };
  },
  // import 引入的组件需要注入到对象中才能使用
  components: { PlatformIcon },
  props: {
    eventInfo: {
      default: () => {},
    },
  },
  // 生命周期 - 创建完成（可以访问当前this 实例）
  created() {},
  // 方法集合
  methods: {
    removeROrN(str) {
      if (str) {
        return str
          .replace(/(\r\n|\n|\r|\\r\\n|\\r|\\n)/g, "")
          .replace(/<[^>]+>/g, "")
          .trim();
      } else {
        return "";
      }
    },
    toDetails(d) {
      const { href } = this.$router.resolve({
        path: "/main/details",
        query: {
          msgKey: d.msgKey,
          situation: d.situation,
          sourcePath: this.$route.path,
          sourceName: this.$route.name,
          keyword: d.matchWords,
          matchWords: d.matchWords,
        },
      });
      this.getLog("事件分析/事件分析详情页", '浏览/'+d.mtitle);
      window.open(href, "_blank");
    },

    getCount() {
      this.loading = true;
      this.count = 0;
      this.pageNo = 1;
      this.dataList = [];
      let params = {
        eventId: this.eventInfo.eventId,
        searchPoisition: "1",
        situations: this.situationId === "0" ? [] : [this.situationId],
        keyword: this.eventInfo.expKeywords,
        startTime: moment(this.eventInfo.monitorStartTime).format(
          "YYYY-MM-DD HH:mm:ss"
        ),
        endTime: moment(this.eventInfo.monitorStopTime).isAfter(moment())
          ? moment().format("YYYY-MM-DD HH:mm:ss")
          : moment(this.eventInfo.monitorStopTime).format(
              "YYYY-MM-DD HH:mm:ss"
            ),
      };
      let _this = this;
      this.$http
        .post("/monitor/event/hotMsgList", params, {
          before(request) {
            if (_this.countPreviousRequest) {
              _this.countPreviousRequest.abort();
            }
            if (_this.listPreviousRequest) {
              _this.listPreviousRequest.abort();
            }
            _this.countPreviousRequest = request;
          },
        })
        .then((res) => {
          this.loading = false;
          if (res.body.status === 0 && res.body.data) {
            // this.count = res.body.data.total;
            // if (this.count > 0) {
            //   this.dataList = res.body.data;
            // } else {
            //   this.dataList = [];
            // }
            this.dataList = res.body.data || [];
          } else {
            this.count = 0;
            this.dataList = [];
            this.$Message.error(res.body.message);
          }
        });
    },
  },
  // 计算属性 类似于 data 概念
  computed: {},
  // 监控 data 中的数据变化
  watch: {
    situationId: {
      handler(d) {
        this.getCount();
      },
    },
  },
  //过滤器
  filters: {},
  // 生命周期 - 挂载完成（可以访问 DOM 元素）
  mounted() {
    this.getCount();
  },
};
</script>
<style scoped lang="less">
.LatestNews {
  position: relative;
  .select {
    position: absolute;
    right: 10px;
    top: -40px;
  }
  .listBox {
    height: 350px;
    overflow-y: auto;
    .item {
      padding: 10px 0 10px 10px;
      border-bottom: 1px solid #eee;
      .num {
        margin-right: 10px;
        font-weight: 400;
      }
      .title {
        font-weight: 600;
        line-height: 24px;
      }
      .content {
        line-height: 24px;
      }
      .weiboTitle {
      }
      .remark {
        margin-top: 10px;
        display: flex;
        align-content: center;
        span {
          margin-right: 10px;
          font-size: 14px;
          color: #999;
        }
        .source {
          display: inline-block;
          max-width: 200px;
        }
      }
    }
  }
}
</style>
