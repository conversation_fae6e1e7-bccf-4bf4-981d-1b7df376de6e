<template>
  <div>
    <!-- <FrameName /> -->
    <div class="eventInfo flex sb">
      <div class="analyze flex sb">
        <div
          style="
            display: flex;
            width: 100%;
            line-height: 100px;
            text-align: center;
            padding-top: 45px;
          "
        >
          <div style="font-size: 30px; font-weight: 600; width: 100%;">
            {{ eventInfo.eventName }}
          </div>
          <div class="topControls flex">
            <div class="item">
              <Poptip
                transfer
                confirm
                title="
                 确认将此事件推送到山东通群吗？
                "
                @on-ok="handleTs"
              >
                <svg-icon icon-class="推送山东通"></svg-icon>推送到山东通
              </Poptip>
            </div>

            <div @click="batchDetection" class="item">
              <svg-icon icon-class="探测 -蓝"></svg-icon>启动信息探测
            </div>
            <div @click="updateEvent()" class="item">
              <svg-icon icon-class="更新"></svg-icon>点击更新数据
            </div>
          </div>
        </div>

        <OuterFrame title="事件描述" style="width: 100%;" :loading="loading">
          <EventDescription
            @saveEdit="saveEdit"
            :eventInfo="eventInfo"
            v-if="eventInfo.eventId"
          />
        </OuterFrame>
        <OuterFrame :loading="loading" title="首发情况" style="width: 100%;">
          <InitialSituation
            @saveEdit="saveEdit"
            :eventInfo="eventInfo"
            v-if="eventInfo.eventId"
          />
        </OuterFrame>
        <OuterFrame :loading="loading" title="数据概览" style="width: 100%;">
          <DataOverview
            :eventInfo="eventInfo"
            @openInfoList="openInfoList"
            v-if="eventInfo.eventId"
            :key="key"
            :showBatchDetection.sync="showBatchDetection"
          />
        </OuterFrame>
        <OuterFrame :loading="loading" title="传播趋势" style="width: 100%;">
          <PropagationTend
            v-if="eventInfo.eventId"
            :key="key"
            :eventInfo="eventInfo"
            @openInfoList="openInfoList"
          />
        </OuterFrame>
        <OuterFrame :loading="loading" title="热榜分析" style="width: 100%;">
          <HotListAnalyze
            v-if="eventInfo.eventId"
            :key="key"
            :eventInfo="eventInfo"
            @openInfoList="openInfoList"
          />
        </OuterFrame>
        <OuterFrame :loading="loading" title="最热信息" style="width: 49%;">
          <latestHotNews
            v-if="eventInfo.eventId"
            :eventInfo="eventInfo"
            :key="key"
          />
        </OuterFrame>
        <OuterFrame :loading="loading" title="最新信息" style="width: 50%;">
          <LatestNews
            v-if="eventInfo.eventId"
            :eventInfo="eventInfo"
            :key="key"
          />
        </OuterFrame>
        <OuterFrame
          :loading="loading"
          title="情感倾向分析"
          style="width: 49%;"
        >
          <AffectiveTendency v-if="eventInfo.eventId" />
        </OuterFrame>
        <OuterFrame :loading="loading" title="热点词云" style="width: 50%;">
          <HotWord v-if="eventInfo.eventId" @openInfoList="openInfoList" />
        </OuterFrame>

        <OuterFrame
          :loading="loading"
          title="活跃信源分析"
          style="width: 49%;"
        >
          <SourceAnalysis
            v-if="eventInfo.eventId"
            @openInfoList="openInfoList"
          />
        </OuterFrame>
        <OuterFrame
          :loading="loading"
          title="转载量TOP10"
          style="width: 50%;"
        >
          <TopTen v-if="eventInfo.eventId" :eventInfo="eventInfo" ref="echartTop" @openInfoList="openInfoList" />
        </OuterFrame>


        <OuterFrame title="观点分析" style="width: 100%;">
          <ViewpointAnalysis v-if="eventInfo.eventId" />
        </OuterFrame>
        <OuterFrame title="地域分布" style="width: 100%;">
          <RegionalDistribution v-if="eventInfo.eventId" />
        </OuterFrame>
      </div>
      <div class="promptSheet">
        <div class="tabs">
          <div
            :class="['item', 'cp', tabId === '1' ? 'active' : '']"
            @click="tabId = '1'"
          >
            事件脉络
          </div>
          <div
            :class="['item', 'cp', tabId === '2' ? 'active' : '']"
            @click="tabId = '2'"
          >
            关联提示单
          </div>
        </div>
        <EventVein v-show="tabId === '1'" :eventInfo="eventInfo" />
        <PromptSheet
          v-show="tabId === '2'"
          :data="eventInfo.promptMsg"
          :loading="loading"
        />
      </div>
      <InfoList
        :switchStatus="switchStatus"
        :params="params"
        @on-cancel="switchStatus = false"
        @updateEvent="updateEvent"
      />
    </div>
  </div>
</template>
<script>
// 这里可以导入其他文件（比如：组件，工具 js，第三方插件 js，json 文件，图片文件等等）
// 例如：import  《组件名称》  from '《组件路径》 ';
import OuterFrame from "./components/outerFrame.vue"; //外框
import EventDescription from "./components/eventDescription.vue"; //事件描述
import InitialSituation from "./components/InitialSituation";
import DataOverview from "./components/dataOverview.vue"; //数据概览
import PropagationTend from "./components/propagationTend.vue"; //传播趋势
import HotListAnalyze from "./components/hotListAnalyze.vue"; //热榜分析
import HotWord from "./components/hotWord.vue"; //热点词云
import LatestNews from "./components/latestNews"; //最新信息
import SourceAnalysis from "./components/sourceAnalysis"; //活跃信源分析
import AffectiveTendency from "./components/affectiveTendency"; //情感倾向
import RegionalDistribution from "./components/regionalDistribution"; //地域分布
import ViewpointAnalysis from "./components/viewpointAnalysis"; //观点分析
import PromptSheet from "./components/promptSheet.vue"; //关联提示单
import EventVein from "./components/EventVein.vue"; //事件脉络
import InfoList from "./components/infoList.vue";
import moment from "moment/moment"; //左侧浮动信息列表
import TopTen from "./components/topTenNew.vue"; //活跃信源分析
import latestHotNews from "./components/latestHotNews.vue"; //最热信息

export default {
  data() {
    // 这里存放数据
    return {
      eventInfo: {},
      loading: false,
      switchStatus: false,
      params: {},
      key: 1,
      tabId: "1",
      showBatchDetection: false,
    };
  },
  // import 引入的组件需要注入到对象中才能使用
  components: {
    OuterFrame,
    EventDescription,
    InitialSituation,
    DataOverview,
    PropagationTend,
    HotListAnalyze,
    HotWord,
    LatestNews,
    SourceAnalysis,
    AffectiveTendency,
    RegionalDistribution,
    ViewpointAnalysis,
    PromptSheet,
    EventVein,
    InfoList,
    TopTen,
    latestHotNews,
  },
  props: {},
  // 生命周期 - 创建完成（可以访问当前this 实例）
  created() {},
  // 方法集合
  methods: {
    openInfoList(obj) {
      let nowStartTime = obj.startTime1
        ? obj.startTime1
        : this.eventInfo.monitorStartTime;
      let nowStopTime = obj.stopTime1
        ? obj.stopTime1
        : this.eventInfo.monitorStopTime;
      this.params = {
        keyword: this.eventInfo.expKeywords,
        startTime: nowStartTime,
        endTime: moment(nowStopTime).isAfter(moment())
          ? moment().valueOf()
          : nowStopTime,
        ...obj,
      };
      this.switchStatus = true;
    },
    saveEdit(obj) {
      let params = {
        ...this.eventInfo,
        ...obj,
      };
      this.$http.post("/monitor/event/editEvent", params).then((res) => {
        if (res.body.status === 0) {
          this.$Message.success("修改成功");
          this.getEventInfo();
        }
      });
    },
    getEventInfo() {
      return new Promise((resolve) => {
        this.loading = true;
        let params = {
          eventId: this.$route.query.eventId,
        };
        this.$http.get("/monitor/event/detail", { params }).then((res) => {
          if (res.body.status !== 0) {
            return false;
          }
          this.eventInfo = res.body.data;
          document.title = "事件-" + this.eventInfo.eventName;
          resolve(this.eventInfo.eventName);
          this.loading = false;
        });
      });
    },
    updateEvent() {
      this.loading = true;
      let params = {
        eventIds: this.$route.query.eventId,
      };
      this.$http
        .get("/monitor/event/updateEventTimer", { params })
        .then((res) => {
          if (res.body.status !== 0) {
            return false;
          }
          this.loading = false;
          this.key++;
          this.$Message.success("更新数据成功");
        });
    },
    // 批量探测
    batchDetection() {
      this.$http
        .post(
          "/monitor/event/startMsgDetection?eventId=" +
            this.$route.query.eventId,
          {}
        )
        .then((res) => {
          if (res.body.status === 0) {
            this.$Message.success("探测已启动，稍后刷新页面查看结果");
          } else {
            this.$Message.error(res.body.message);
          }
        })
        .catch((err) => {
          this.$Message.error(err.body.message);
        });
    },
    handleTs() {
      this.$http
        .get(
          "/monitor/event/eventPush?eventId=" + this.$route.query.eventId,
          {}
        )
        .then((res) => {
          if (res.body.status === 0) {
            this.$Message.success("已推送至山东通群");
          } else {
            this.$Message.error(res.body.message);
          }
        })
        .catch((err) => {
          this.$Message.error(err.body.message);
        });
    },
  },
  // 计算属性 类似于 data 概念
  computed: {},
  // 监控 data 中的数据变化
  watch: {},
  //过滤器
  filters: {},
  // 生命周期 - 挂载完成（可以访问 DOM 元素）
  mounted() {
    this.getEventInfo().then((name) => {
      this.getLog("事件分析/事件列表", "查看事件分析/" + name);
    });
  },
};
</script>
<style scoped lang="less">
.eventInfo {
  margin-top: 20px;
  width: 1727px;
  border-radius: 4px;

  & > div {
    height: calc(~"100vh - 30px");
    overflow-y: auto;
    background-color: #fff;
  }

  .analyze {
    flex-wrap: wrap;
    width: 1100px;
    padding: 10px;
    position: relative;

    /deep/ .controls {
      position: absolute;
      right: 10px;
      top: 10px;
      align-items: center;

      .svg-icon {
        margin: 5px;
        cursor: pointer;
      }
      & > span {
        color: #5585ec;
        font-size: 14px;
        margin: 0 5px;
        cursor: pointer;
      }
    }

    .eventName {
      font-size: 30px;
      line-height: 100px;
      font-weight: 600;
      text-align: center;
      width: 100%;
    }
  }

  .promptSheet {
    width: 620px;
    position: relative;
    overflow: hidden;
    .tabs {
      overflow: hidden;
      border-top-left-radius: 10px;
      border-top-right-radius: 10px;
      position: relative;
      height: 60px;
      .item {
        display: flex;
        align-items: center;
        justify-content: center;
        position: absolute;
        text-align: center;
        line-height: 60px;
        width: 53%;
        font-size: 16px;
        &:nth-child(1) {
          clip-path: polygon(0% 0%, 100% 0%, 90% 100%, 0% 100%);
          left: 0;
        }
        &:nth-child(2) {
          clip-path: polygon(10% 0%, 100% 0%, 100% 100%, 0% 100%);
          right: 0;
        }
      }
      .active {
        background-color: #5585ec;
        color: #fff;
      }
    }
  }
}
.topControls {
  position: absolute;
  right: 10px;
  top: 20px;
  .item {
    padding: 0 5px;
    display: flex;
    align-items: center;
    // width: 110px;
    justify-content: center;
    height: 50px;
    background: #e6f7ff;
    border: 1px solid #5585ec;
    border-radius: 4px;
    font-size: 14px;
    line-height: 24px;
    cursor: pointer;
    text-align: center;
    margin: 0 2px;
    height: 24px;
  }
}
</style>
