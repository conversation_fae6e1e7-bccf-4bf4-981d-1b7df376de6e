<template>
  <div class="task-audit">
    <div class="audit-container">
      <div class="main">
        <div class="mleft">
         <!-- 任务信息 -->
        <div class="section">
          <div class="section-title">任务信息</div>
          <div class="section-content">
            <div class="form-item">
              <div class="label">任务名：</div>
              <div class="input-wrapper">
                <Input v-model="taskData.name" placeholder="请输入任务名称" :disabled="!isEditable" />
              </div>
            </div>
            <div class="form-item">
              <div class="label"></div>
              <div class="article-list input-wrapper">

              <div class="article-header">
                <div class="col-title">文章标题：</div>
                <div class="col-action">文章链接：</div>
                <div class="col-platform">所属平台：</div>
              </div>
              <div v-for="(article, index) in taskData.articles" :key="index" class="article-item">
                <div class="col-title " :title="article.title"><span class="oneline">{{ article.title }}</span></div>
                <div class="col-action"><a :href="article.url" target="_blank" class="view-link oneline" :title="article.url">{{article.url}}</a></div>
                <div class="col-platform">{{article.platform}}</div>
              </div>
            </div>
            </div>


            <div class="form-item">
              <div class="label">备注：</div>
              <div class="input-wrapper">
                <Input v-model="taskData.remark" type="textarea" :rows="4" placeholder="请输入备注信息" />
              </div>
            </div>

          </div>
        </div>

        <!-- 任务审核 -->
        <div class="section" v-if="showAuditHistory">
           <div class="section-title">任务审核</div>
           <div class="section-content">
             <div class="audit-history">
               <!-- 初审信息 -->
               <div class="audit-item" v-if="taskData.reviewerName">
                 <div class="audit-label">初审人：</div>
                 <div class="audit-value">{{ taskData.reviewerName }}</div>
               </div>
               <div class="audit-item" v-if="taskData.reviewResult !== undefined">
                 <div class="audit-label">初审结果：</div>
                 <div class="audit-value">{{ taskData.reviewResult == 1 ? '通过' : '不通过' }}</div>
               </div>
               <div class="audit-item" v-if="taskData.auditTime">
                 <div class="audit-label">初审时间：</div>
                 <div class="audit-value">{{ formatDate(taskData.auditTime) }}</div>
               </div>
             </div>
           </div>
        </div>

        <!-- 审核结果 -->
        <div class="section">
          <div class="section-title">审核结果</div>
          <div class="section-content">
            <div class="audit-result">
              <RadioGroup v-model="auditResult" @on-change="handleAuditResultChange">
                <Radio :label="1">审核通过</Radio>
                <Radio :label="2">审核不通过</Radio>
              </RadioGroup>
            </div>


          </div>
          <div class="section-title">任务明细</div>
          <div class="section-content">
             <!-- 审核通过内容 -->
             <div v-show="auditResult === 1" class="audit-pass-content">
              <div class="form-item">
                <div class="label required">任务类型：</div>
                <div class="input-wrapper">
                  <RadioGroup v-model="auditData.taskType" disabled>
                    <Radio label="普通">普通</Radio>
                  </RadioGroup>
                </div>
              </div>

              <div class="form-item">
                <div class="label required">执行类型：</div>
                <div class="input-wrapper">
                  <Checkbox v-model="auditData.executeTypeChecked" disabled>举报投诉</Checkbox>
                </div>
              </div>

              <div class="form-item">
                <div class="label required">任务等级：</div>
                <div class="input-wrapper">
                  <RadioGroup v-model="auditData.taskLevel">
                    <Radio :label="1">高</Radio>
                    <Radio :label="2">中</Radio>
                    <Radio :label="3">低</Radio>
                  </RadioGroup>
                </div>
              </div>

              <div class="form-item">
                <div class="label required">任务时间：</div>
                <div class="input-wrapper time-options">
                  <RadioGroup v-model="auditData.timeType">
                    <Radio :label="1">1小时内</Radio>
                    <Radio :label="2">2小时内</Radio>
                    <Radio :label="3">24小时内</Radio>
                    <Radio :label="4">48小时内</Radio>
                    <Radio :label="5">72小时内</Radio>
                    <Radio :label="6">自定义结束时间</Radio>
                  </RadioGroup>
                  <DatePicker
                    v-if="auditData.timeType === 6"
                    type="datetime"
                    placeholder="选择结束时间"
                    v-model="auditData.endTime"
                    style="width: 200px; margin-left: 10px;"
                    :options="dateOptions"
                  ></DatePicker>
                </div>
              </div>


            </div>

            <!-- 审核不通过内容 -->
            <div v-show="auditResult === 2" class="audit-reject-content">
              <div class="form-item">
                <div class="label required">未通过原因：</div>
                <div class="input-wrapper">
                  <Input v-model="auditData.reason" type="textarea" :rows="6" placeholder="请输入未通过原因" />
                </div>
              </div>
            </div>
          </div>
        </div>
        </div>
        <div class="mright" >
          <div class="section" v-show="auditResult === 1">
            <div class="section-title">队伍选择</div>
            <!-- 队伍选择 -->
            <div class="team-selection">
                <div class="team-container">
                  <div class="team-tree">

                    <div class="tree-container">
                      <div v-if="teamTreeData && teamTreeData.length > 0">
                        <Tree
                          :data="teamTreeData"
                          show-checkbox
                          @on-check-change="handleTeamSelect"
                          check-directly
                          :render-after-expand="false"
                        ></Tree>
                      </div>
                      <div v-else class="no-data">
                        <Spin v-if="loading">
                          <Icon type="ios-loading" size="18" class="spin-icon-load"></Icon>
                          <div>加载中...</div>
                        </Spin>
                        <div v-else>暂无数据</div>
                      </div>
                    </div>
                  </div>
                  <div class="selected-teams">
                    <div class="team-title">已选中 <span>{{ totalSelectedCount }}</span>人</div>
                    <div class="selected-list">
                      <div v-for="(team, index) in selectedTeams" :key="index" class="selected-team-item">
                        <span>{{ team.name }}</span>
                        <span>(<i>{{ team.count }}</i>/{{ team.total }})</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
          </div>
        </div>
      </div>



      <!-- 操作按钮 -->
      <div class="action-buttons">
        <Button @click="handleCancel">取消</Button>
        <Button type="primary" @click="handleSubmit" :loading="submitting">提交</Button>
      </div>
    </div>
  </div>
</template>

<script>
import { formatDate } from '@/util/date';

export default {
  name: 'TaskAudit',
  data() {
    return {
      taskId: null,
      isEditable: true,
      submitting: false,
      showAuditHistory: false, // 是否显示审核历史

      // 任务数据
      taskData: {
        id: '',
        name: '',
        articles: [],
        remark: '',
        reviewerName: '',
        reviewResult: null,
        auditTime: '',
        secondReviewerName: '',
        secondReviewResult: null,
        secondAuditTime: ''
      },

      // 审核结果
      auditResult: 1, // 1: 通过, 2: 不通过

      // 审核数据
      auditData: {
        taskType: '普通',
        executeType: '举报投诉',
        executeTypeChecked: true, // 执行类型复选框状态
        taskLevel: 1, // 1: 高, 2: 中, 3: 低
        timeType: 1, // 1: 1小时内, 2: 2小时内, 3: 24小时内, 4: 48小时内, 5: 72小时内, 6: 自定义
        endTime: '',
        reason: '',
        selectedTeamIds: []
      },

      // 队伍树数据
      teamTreeData: [],
      loading: true, // 加载状态

      // 已选中的队伍
      selectedTeams: [],
      totalSelectedCount: 0,

      // 日期选择器配置
      dateOptions: {
        disabledDate: (date) => {
          return date && date.getTime() < Date.now() - 86400000; // 禁用今天之前的日期
        }
      }
    };
  },
  created() {
    // 从URL获取任务ID
    this.taskId = this.$route.query.id;
    if (this.taskId) {
      this.fetchTaskDetail();
    }

    // 获取队伍树数据
    this.fetchTeamTreeData();
  },
  methods: {
    // 格式化日期
    formatDate(date, fmt) {
      return formatDate(date, fmt);
    },

    // 获取任务详情
    fetchTaskDetail() {
      this.$http.get('/linkAge/taskInfo', { params: { id: this.taskId } })
        .then(res => {
          if (res.body.status === 0 && res.body.data) {
            const { taskInfo, articleInfoList } = res.body.data;
            console.log('任务详情:', taskInfo);
            console.log('文章列表:', articleInfoList);

            // 设置任务数据
            this.taskData = {
              id: taskInfo.id,
              name: taskInfo.name || '',
              remark: taskInfo.remark || '',
              articles: articleInfoList.map(article => ({
                title: article.title || '',
                platform: article.platform || '',
                url: article.url || ''
              })),
              status: taskInfo.status,
              createTime: taskInfo.createTime,
              promoterName: taskInfo.promoterName,
              promoterOrganName: taskInfo.promoterOrganName,
              // 审核相关字段
              reviewerName: taskInfo.reviewerName || '',
              reviewResult: taskInfo.reviewResult,
              auditTime: taskInfo.auditTime || '',
              secondReviewerName: taskInfo.secondReviewerName || '',
              secondReviewResult: taskInfo.secondReviewResult,
              secondAuditTime: taskInfo.secondAuditTime || ''
            };

            // 判断是否显示审核历史（主任审核时显示初审信息）
            this.showAuditHistory = taskInfo.reviewerName && taskInfo.reviewResult !== undefined;

            console.log(this.taskData)

            // 打印绑定后的数据
            console.log('绑定后的数据:', this.taskData);

            // 如果任务已经有审核结果，则设置相应的值
            if (taskInfo.taskType) {
              this.auditData.taskType = taskInfo.taskType;
            }

            if (taskInfo.executeType) {
              this.auditData.executeType = taskInfo.executeType;
              // 如果执行类型是"举报投诉"，则勾选复选框
              this.auditData.executeTypeChecked = taskInfo.executeType === '举报投诉';
            }

            if (taskInfo.taskLevel) {
              this.auditData.taskLevel = parseInt(taskInfo.taskLevel);
            }

            if (taskInfo.timeType) {
              this.auditData.timeType = parseInt(taskInfo.timeType);
            }

            if (taskInfo.endTime) {
              this.auditData.endTime = new Date(taskInfo.endTime);
            }
          } else {
            this.$Message.error(res.body.message || '获取任务详情失败');
          }
        })
        .catch(err => {
          this.$Message.error('获取任务详情失败');
          console.error(err);
        });
    },

    // 处理审核结果变更
    handleAuditResultChange(value) {
      this.auditResult = value;

      // 如果切换到审核不通过，清空已选择的队伍
      if (value === 2) {
        this.selectedTeams = [];
        this.totalSelectedCount = 0;
        this.auditData.selectedTeamIds = [];
      }
    },

    // 获取队伍树数据
    fetchTeamTreeData() {
      this.loading = true; // 开始加载

      this.$http.get('/linkAge/getGroupList')
        .then(res => {
          console.log('API返回原始数据:', res.body);
          if (res.body.status === 1) { // 注意：API返回status为1表示成功
            // 从body.data中获取数据
            const data = res.body.body && res.body.body.data;
            if (data && Array.isArray(data)) {
              // 转换数据为Tree组件所需格式
              this.teamTreeData = this.convertToTreeData(data);
              console.log('转换后的队伍树数据:', this.teamTreeData);
            } else {
              console.error('API返回的数据结构不符合预期:', res.body);
              this.$Message.error('获取队伍树数据失败: 数据结构不符合预期');
            }
          } else {
            this.$Message.error(res.body.msg || '获取队伍树数据失败');
          }
        })
        .catch(err => {
          this.$Message.error('获取队伍树数据失败');
          console.error(err);
        })
        .finally(() => {
          this.loading = false; // 结束加载
        });
    },

    // 将API返回的数据转换为Tree组件所需格式
    convertToTreeData(data) {
      if (!data || !Array.isArray(data)) return [];

      // 递归转换数据
      const convertNode = (node, level = 0) => {
        // 基本节点信息
        const treeNode = {
          title: node.name,
          expand: level === 0, // 第一层节点默认展开，其他层级默认收起
          id: node.code,
          nodeKey: node.code,
          isOrg: node.isOrg,
          userCount: node.userCount,
          origin: node // 保存原始数据，以便在处理队伍选择时使用
        };

        // 如果有子部门，递归处理
        if (node.childDepartment && Array.isArray(node.childDepartment) && node.childDepartment.length > 0) {
          treeNode.children = node.childDepartment.map(child => convertNode(child, level + 1));

          // 如果是组织，显示人数
          if (node.isOrg === 1 && node.userCount > 0) {
            treeNode.title = `${node.name}(${node.userCount})`;
          }
        } else {
          // 叶子节点（个人或无子部门的组织）
          treeNode.checked = false;

          // 如果是个人节点，显示完整名称
          if (node.isOrg === 0) {
            treeNode.title = node.name; // 个人名称已经包含手机号
          }
        }

        return treeNode;
      };

      return data.map(item => convertNode(item, 0));
    },

    // 处理队伍选择
    handleTeamSelect(selectedNodes) {
      // 处理选中的队伍
      this.selectedTeams = [];
      this.totalSelectedCount = 0;
      console.log('选中的节点:', selectedNodes);

      // 自动展开被选中的父节点
      this.expandSelectedParentNodes(selectedNodes);

      if (selectedNodes && selectedNodes.length > 0) {
        // 处理选中的队伍
        const processedTeams = new Map();

        // 创建一个映射来存储每个组织的总人数
        const orgTotalMap = new Map();

        // 首先遍历树形数据，获取每个组织的总人数
        const collectOrgTotals = (nodes) => {
          nodes.forEach(node => {
            // 如果是组织节点且有userCount
            if (node.isOrg === 1 && node.userCount) {
              // 使用组织名称作为键
              const orgName = node.title.replace(/\(\d+\)$/, '').trim();
              orgTotalMap.set(orgName, node.userCount);
            }

            // 递归处理子节点
            if (node.children && node.children.length > 0) {
              collectOrgTotals(node.children);
            }
          });
        };

        // 收集所有组织的总人数
        collectOrgTotals(this.teamTreeData);

        // 创建一个Set来存储已处理的节点ID，避免重复计算
        const processedNodeIds = new Set();

        // 递归查找所有选中的叶子节点和组织节点
        const findSelectedNodes = (nodes) => {
          let allSelectedNodes = [];

          nodes.forEach(node => {
            // 如果节点已经处理过，跳过
            if (processedNodeIds.has(node.nodeKey)) {
              return;
            }

            // 标记节点为已处理
            processedNodeIds.add(node.nodeKey);

            if (node.checked) {
              // 如果节点被选中
              if (node.children && node.children.length > 0) {
                // 如果是组织节点且被选中，添加该组织下的所有人员
                if (node.isOrg === 1) {
                  // 添加组织本身
                  allSelectedNodes.push(node);

                  // 递归查找所有子节点
                  const findAllLeafNodes = (childNodes) => {
                    let leafNodes = [];
                    childNodes.forEach(childNode => {
                      // 如果节点已经处理过，跳过
                      if (processedNodeIds.has(childNode.nodeKey)) {
                        return;
                      }

                      // 标记节点为已处理
                      processedNodeIds.add(childNode.nodeKey);

                      if (childNode.children && childNode.children.length > 0) {
                        leafNodes = [...leafNodes, ...findAllLeafNodes(childNode.children)];
                      } else {
                        leafNodes.push(childNode);
                      }
                    });
                    return leafNodes;
                  };

                  // 添加该组织下的所有叶子节点
                  allSelectedNodes = [...allSelectedNodes, ...findAllLeafNodes(node.children)];
                } else {
                  // 如果不是组织节点，递归处理
                  allSelectedNodes = [...allSelectedNodes, ...findSelectedNodes(node.children)];
                }
              } else {
                // 叶子节点且被选中
                allSelectedNodes.push(node);
              }
            } else if (node.children && node.children.length > 0) {
              // 如果节点未被选中但有子节点，递归处理子节点
              allSelectedNodes = [...allSelectedNodes, ...findSelectedNodes(node.children)];
            }
          });

          return allSelectedNodes;
        };

        // 获取所有选中的节点
        const selectedAllNodes = findSelectedNodes(selectedNodes);

        // 过滤出叶子节点（个人）
        const selectedLeafNodes = selectedAllNodes
          .filter(node => !node.children || node.children.length === 0);

        // 打印选中的叶子节点信息，用于调试
        console.log('选中的叶子节点:', selectedLeafNodes.map(node => ({
          nodeKey: node.nodeKey,
          id: node.id,
          title: node.title,
          isOrg: node.isOrg,
          origin: node.origin ? {
            code: node.origin.code,
            name: node.origin.name,
            isOrg: node.origin.isOrg
          } : null
        })));

        // 创建一个映射来存储每个人员所属的组织
        const personOrgMap = new Map();

        // 处理选中的叶子节点
        selectedLeafNodes.forEach(node => {
          // 提取组织名称
          let orgName = node.title;
          let parentOrgName = '';

          // 如果是个人节点，从名称中提取组织
          if (node.isOrg === 0) {
            // 尝试从nameFullPath中提取组织名称
            if (node.origin && node.origin.nameFullPath) {
              const pathParts = node.origin.nameFullPath.split('/');
              if (pathParts.length > 1) {
                parentOrgName = pathParts[pathParts.length - 2];
                orgName = parentOrgName;
              }
            } else {
              // 如果没有nameFullPath，尝试从title中提取
              const match = node.title.match(/^(.*?)\(/);
              if (match) {
                orgName = match[1];
              }
            }

            // 记录该人员所属的组织
            personOrgMap.set(node.nodeKey, orgName);
          } else if (node.isOrg === 1) {
            // 如果是组织节点，去掉括号中的数字
            orgName = node.title.replace(/\(\d+\)$/, '').trim();
          }

          // 获取组织的总人数（确保不超过API返回的实际人数）
          const totalCount = orgTotalMap.get(orgName) || 1;

          // 统计每个组织的人数
          if (!processedTeams.has(orgName)) {
            processedTeams.set(orgName, {
              name: orgName,
              count: 1,
              total: totalCount
            });
          } else {
            const team = processedTeams.get(orgName);
            team.count += 1;

            // 确保count不超过total
            if (team.count > team.total) {
              team.count = team.total;
            }
          }
        });

        this.selectedTeams = Array.from(processedTeams.values());

        // 确保每个团队的选中人数不超过其总人数
        this.selectedTeams.forEach(team => {
          if (team.count > team.total) {
            team.count = team.total;
          }
        });

        // 计算总选中人数
        this.totalSelectedCount = this.selectedTeams.reduce((sum, team) => sum + team.count, 0);

        // 收集选中的队伍ID - 确保使用人员的code字段
        this.auditData.selectedTeamIds = selectedLeafNodes
          .filter(node => node.isOrg === 0) // 只选择个人节点
          .map(node => node.origin && node.origin.code ? node.origin.code : node.nodeKey); // 优先使用origin.code
      }
    },

    // 处理取消
    handleCancel() {
      // 直接跳转到联动处置列表页，而不是使用 go(-1)
      this.$router.push({
        path: '/main/jointDisposal/taskList'
      });
    },

    // 处理提交
    handleSubmit() {
      if (this.validateForm()) {
        this.submitting = true;

        // 构建提交数据
        const submitData = {
          id: this.taskData.id,
          name: this.taskData.name,
          status: this.auditResult.toString(), // 1: 审核通过, 2: 审核不通过
          remark: this.taskData.remark
        };

        // 根据审核结果添加不同的数据
        if (this.auditResult === 1) {
          // 审核通过
          // 任务类型和执行类型只在前台展示，不需要传递给接口
          submitData.taskLevel = this.auditData.taskLevel.toString();
          submitData.timeType = this.auditData.timeType.toString();

          // 只有自定义结束时间才需要传递endTime参数
          if (this.auditData.timeType === 6) {
            // 确保有选择结束时间
            if (!this.auditData.endTime) {
              this.$Message.error('请选择自定义结束时间');
              this.submitting = false;
              return;
            }

            // 使用用户选择的完整日期时间，格式化为 yyyy-MM-dd HH:mm:ss
            const selectedDate = new Date(this.auditData.endTime);
            const year = selectedDate.getFullYear();
            const month = String(selectedDate.getMonth() + 1).padStart(2, '0');
            const day = String(selectedDate.getDate()).padStart(2, '0');
            const hours = String(selectedDate.getHours()).padStart(2, '0');
            const minutes = String(selectedDate.getMinutes()).padStart(2, '0');
            const seconds = String(selectedDate.getSeconds()).padStart(2, '0');

            // 构建正确格式的日期字符串，保留用户选择的完整时间
            submitData.endTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;

            console.log('提交的结束时间:', submitData.endTime);
          }
          // 其他时间类型不需要传递endTime参数

          // 添加选中的人员ID，使用逗号分隔
          // 确保使用人员的code字段而不是索引值
          submitData.person = this.auditData.selectedTeamIds.join(',');
          console.log('提交的人员ID:', submitData.person);
        } else {
          // 审核不通过
          submitData.reason = this.auditData.reason;
        }

        // 发送审核请求
        this.$http.post('/linkAge/auditTask', submitData, {
          headers: {
            'Content-Type': 'application/json'
          }
        })
          .then(res => {
            if (res.body.status === 0) {
              this.$Message.success('审核操作成功');
              // 审核成功后，延迟1秒跳转回联动处置列表页
              setTimeout(() => {
                this.$router.push({
                  path: '/main/jointDisposal/taskList'
                });
              }, 1000);
            } else {
              this.$Message.error(res.body.message || '审核操作失败');
            }
          })
          .catch(err => {
            this.$Message.error('审核操作失败，请重试');
            console.error(err);
          })
          .finally(() => {
            this.submitting = false;
          });
      }
    },

    // 自动展开被选中的父节点
    expandSelectedParentNodes(selectedNodes) {
      if (!selectedNodes || selectedNodes.length === 0) return;

      // 递归更新树节点的展开状态
      const updateTreeNodeExpand = (nodes) => {
        nodes.forEach(node => {
          // 如果节点被选中且有子节点，设置为展开状态
          if (node.checked && node.children && node.children.length > 0) {
            node.expand = true;

            // 递归处理子节点
            updateTreeNodeExpand(node.children);
          }
        });
      };

      // 更新选中节点的展开状态
      updateTreeNodeExpand(selectedNodes);

      // 更新整个树的展开状态
      const updateEntireTree = (treeData, selectedNodeIds) => {
        treeData.forEach(node => {
          // 如果节点ID在选中的节点ID列表中，设置为展开状态
          if (selectedNodeIds.includes(node.nodeKey)) {
            node.expand = true;
          }

          // 递归处理子节点
          if (node.children && node.children.length > 0) {
            updateEntireTree(node.children, selectedNodeIds);
          }
        });
      };

      // 获取所有选中节点的ID
      const selectedNodeIds = selectedNodes
        .filter(node => node.checked)
        .map(node => node.nodeKey);

      // 更新整个树的展开状态
      updateEntireTree(this.teamTreeData, selectedNodeIds);
    },

    // 表单验证
    validateForm() {
      if (!this.taskData.name.trim()) {
        this.$Message.error('请输入任务名称');
        return false;
      }

      if (this.auditResult === 1) {
        // 审核通过验证
        if (this.auditData.timeType === 6) {
          if (!this.auditData.endTime) {
            this.$Message.error('请选择自定义结束时间');
            return false;
          }

          // 验证结束时间不早于当前时间
          const now = new Date();
          if (this.auditData.endTime < now) {
            this.$Message.error('结束时间不能早于当前时间');
            return false;
          }
        }

        if (this.selectedTeams.length === 0) {
          this.$Message.error('请选择至少一个队伍');
          return false;
        }
      } else {
        // 审核不通过验证
        if (!this.auditData.reason.trim()) {
          this.$Message.error('请输入未通过原因');
          return false;
        }
        // 审核不通过时不需要验证队伍选择
      }

      return true;
    },

    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    }
  }
};
</script>

<style lang="less" scoped>
/deep/.ivu-input{
  font-size:14px;
}
.main{
  display: flex;
  flex-direction: row;
  width:100%;
  gap: 30px;
  .mleft{
    flex: 1;
    width:900px;
    height: 100%;
  }
  .mright{
    min-width:720px;
    min-height: 100%;
  }
}
.oneline{
  //单行省略
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  display: block;
}
.task-audit {

  padding-left:0;
  height: 100%;
  display: flex;

  .audit-container {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
    padding: 20px;
    min-height:100%;
    .main{
      flex:1;
    }
    .section {
      margin-bottom: 24px;
      display: flex;
      flex-direction: column;
      height: 100%;

      .section-title {
        font-size: 16px;
        font-weight: 500;
        color: #17233d;
        padding: 8px 0;
        border-bottom: 1px solid #e8eaec;
        margin-bottom: 16px;
        position: relative;

        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 8px;
          height: 16px;
          width: 4px;
          background-color: #2d8cf0;
          border-radius: 2px;
          margin-left: -20px;
        }
      }

      .section-content {
        flex: 1;
        padding: 0 16px;
      }
    }

    .form-item {
      display: flex;
      margin-bottom: 16px;
      justify-content: top;
      align-items: top;

      .label {
        min-width: 100px;
        text-align: right;
        padding-right: 12px;
        line-height: 32px;
        font-size:14px;


        &.required::before {
          content: '*';
          color: #ed4014;
          margin-right: 4px;
        }
      }

      .input-wrapper {
        flex: 1;

        &.time-options {
          display: flex;
          align-items: center;
        }
      }
    }

    .article-list {
      margin: 16px 0;
      border: 1px solid #e8eaec;
      border-radius: 4px;
      overflow: hidden;
      font-size:14px;
      .article-header {
        display: flex;
        background-color: #f8f9fa;
        padding: 10px 16px;
        gap: 20px;
        font-weight: 500;
      }

      .article-item {
        display: flex;
        padding: 12px 16px;
        gap: 20px;
        border-top: 1px solid #e8eaec;
      }

      .col-title {
        flex: 3;
        max-width:42%;
      }

      .col-platform {
        flex: 1;
        text-align: left;
        max-width:15%;
      }

      .col-action {
        max-width:40%;
        flex: 1;
        text-align: left;

        .view-link {
          color: #2d8cf0;
          cursor: pointer;

          &:hover {
            text-decoration: underline;
          }
        }
      }
    }

    .audit-result {
      margin-bottom: 20px;
    }

    .team-selection {
      margin-top: 24px;
      flex: 1;
      .team-container {
        display: flex;
        border-radius: 4px;
        overflow: hidden;
        height: 100%;
        background:#F6F9FF;
        gap: 20px;
        padding:20px;

        .team-tree, .selected-teams {
          flex: 1;
          padding: 16px;
          background-color: white;
          border: 1px solid #e8eaec;
          border-radius: 5px;
          max-height: 700px;
          .team-title {
            font-weight: 500;
            margin-bottom: 12px;
            padding-bottom: 8px;
            font-size:14px;
            border-bottom: 1px solid #e8eaec;
            span{
              color:#60B5FD
            }
          }
        }

        .team-tree {
          border-right: 1px solid #e8eaec;
          max-height:700px;
          overflow:scroll;
          .tree-container {
            overflow-y: auto;
            min-height: 300px;
            overflow-x: hidden;

            .no-data {
              display: flex;
              justify-content: center;
              align-items: center;
              height: 300px;
              color: #999;
              font-size: 14px;

              .spin-icon-load {
                animation: ani-demo-spin 1s linear infinite;
              }
            }

            @keyframes ani-demo-spin {
              from { transform: rotate(0deg); }
              50% { transform: rotate(180deg); }
              to { transform: rotate(360deg); }
            }
          }
        }

        .selected-teams {
          .selected-list {
            display: flex;
            flex-direction: column;
            overflow-y: scroll;
            max-height:640px;
            gap: 5px;

            .selected-team-item {
              font-size:14px;
              padding: 8px 8px;
              background-color: #F7F8FD;
              border-radius: 5px;

              &:last-child {
                border-bottom: none;
              }
              span{
                i{
                  color:#60B5FD
                }
              }
              span:last-child {
                margin-left: 8px;
                color: #808695;
              }
            }
          }
        }
      }
    }

    .action-buttons {
      display: flex;
      justify-content: center;
      margin-top: 32px;

      button {
        margin: 0 8px;
        min-width: 80px;
      }
    }
  }
}

// 审核历史样式
.audit-history {
  .audit-item {
    display: flex;
    margin-bottom: 12px;
    align-items: center;

    .audit-label {
      width: 80px;
      color: #666;
      font-weight: 500;
    }

    .audit-value {
      color: #333;
      flex: 1;
    }
  }
}
</style>
