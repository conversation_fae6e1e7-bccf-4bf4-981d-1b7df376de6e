<template>
  <div class="task-audit">
    <div class="audit-container">
      <div class="mleft">
        <!-- 任务信息 -->
        <div class="section">
          <div class="section-title">任务信息</div>
          <div class="section-content">
            <div class="form-item">
              <div class="label required">任务名称：</div>
              <div class="input-wrapper">
                <Input 
                  v-model="taskData.name" 
                  placeholder="请输入任务名称"
                  :disabled="!isEditable"
                />
              </div>
            </div>

            <div class="form-item">
              <div class="label">备注：</div>
              <div class="input-wrapper">
                <Input 
                  v-model="taskData.remark" 
                  type="textarea" 
                  :rows="3" 
                  placeholder="请输入备注信息"
                  :disabled="!isEditable"
                />
              </div>
            </div>

            <!-- 文章列表 -->
            <div class="form-item">
              <div class="label">文章信息：</div>
              <div class="articles-list">
                <div 
                  v-for="(article, index) in taskData.articles" 
                  :key="index" 
                  class="article-item"
                >
                  <div class="article-info">
                    <div class="article-title">{{ article.title }}</div>
                    <div class="article-meta">
                      <span class="platform">{{ article.platform }}</span>
                      <a :href="article.url" target="_blank" class="article-url">{{ article.url }}</a>
                    </div>
                  </div>
                </div>
              </div>
            </div>

          </div>
        </div>

        <!-- 任务审核历史 -->
        <div class="section">
           <div class="section-title">任务审核</div>
           <div class="section-content">
             <div class="audit-history">
               <!-- 初审信息 -->
               <div class="audit-item">
                 <div class="audit-label">初审人：</div>
                 <div class="audit-value">{{ taskData.reviewerName }}</div>
               </div>
               <div class="audit-item">
                 <div class="audit-label">初审结果：</div>
                 <div class="audit-value">{{ taskData.reviewResult == 1 ? '通过' : '不通过' }}</div>
               </div>
               <div class="audit-item">
                 <div class="audit-label">初审时间：</div>
                 <div class="audit-value">{{ formatDate(taskData.auditTime) }}</div>
               </div>
             </div>
           </div>
        </div>

        <!-- 审核结果 -->
        <div class="section">
          <div class="section-title">审核结果</div>
          <div class="section-content">
            <div class="audit-result">
              <RadioGroup v-model="auditResult" @on-change="handleAuditResultChange">
                <Radio :label="1">审核通过</Radio>
                <Radio :label="2">审核不通过</Radio>
              </RadioGroup>
            </div>
          </div>
          
          <div class="section-title">任务明细</div>
          <div class="section-content">
             <!-- 审核通过内容 -->
             <div v-show="auditResult === 1" class="audit-pass-content">
              <div class="form-item">
                <div class="label required">任务类型：</div>
                <div class="input-wrapper">
                  <RadioGroup v-model="auditData.taskType">
                    <Radio label="普通">普通</Radio>
                  </RadioGroup>
                </div>
              </div>

              <div class="form-item">
                <div class="label required">执行类型：</div>
                <div class="input-wrapper">
                  <div class="execute-type-wrapper">
                    <Select v-model="auditData.executeType" placeholder="请选择执行类型">
                      <Option value="举报投诉">举报投诉</Option>
                      <Option value="其他">其他</Option>
                    </Select>
                    <Checkbox 
                      v-model="auditData.executeTypeChecked" 
                      v-show="auditData.executeType === '举报投诉'"
                    >
                      举报投诉
                    </Checkbox>
                  </div>
                </div>
              </div>

              <div class="form-item">
                <div class="label required">任务等级：</div>
                <div class="input-wrapper">
                  <RadioGroup v-model="auditData.taskLevel">
                    <Radio :label="1">高</Radio>
                    <Radio :label="2">中</Radio>
                    <Radio :label="3">低</Radio>
                  </RadioGroup>
                </div>
              </div>

              <div class="form-item">
                <div class="label required">任务时间：</div>
                <div class="input-wrapper">
                  <RadioGroup v-model="auditData.timeType" @on-change="handleTimeTypeChange">
                    <Radio :label="1">1小时内</Radio>
                    <Radio :label="2">2小时内</Radio>
                    <Radio :label="3">24小时内</Radio>
                    <Radio :label="4">48小时内</Radio>
                    <Radio :label="5">72小时内</Radio>
                    <Radio :label="6">自定义结束时间</Radio>
                  </RadioGroup>
                </div>
              </div>

              <!-- 自定义时间选择 -->
              <div v-show="auditData.timeType === 6" class="form-item">
                <div class="label required">结束时间：</div>
                <div class="input-wrapper">
                  <DatePicker
                    v-model="auditData.endTime"
                    type="datetime"
                    placeholder="请选择结束时间"
                    :options="dateOptions"
                    format="yyyy-MM-dd HH:mm:ss"
                  />
                </div>
              </div>
            </div>

            <!-- 审核不通过内容 -->
            <div v-show="auditResult === 2" class="audit-reject-content">
              <div class="form-item">
                <div class="label required">未通过原因：</div>
                <div class="input-wrapper">
                  <Input 
                    v-model="auditData.reason" 
                    type="textarea" 
                    :rows="4" 
                    placeholder="请输入未通过原因"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        </div>
        <div class="mright" >
          <div class="section" v-show="auditResult === 1">
            <div class="section-title">队伍选择</div>
            <!-- 队伍选择 -->
            <div class="team-selection">
              <div class="team-tree-container">
                <div class="tree-header">
                  <span>选择执行队伍</span>
                  <span class="selected-count">已选择：{{ totalSelectedCount }}人</span>
                </div>
                <div class="tree-content">
                  <Tree
                    ref="teamTree"
                    :data="teamTreeData"
                    show-checkbox
                    check-strictly
                    @on-check-change="handleTeamSelection"
                  />
                </div>
              </div>
            </div>
          </div>

          <!-- 已选择的队伍列表 -->
          <div class="section" v-show="auditResult === 1 && selectedTeams.length > 0">
            <div class="section-title">已选择队伍</div>
            <div class="selected-teams">
              <div class="selected-list">
                <div 
                  v-for="(team, index) in selectedTeams" 
                  :key="index" 
                  class="selected-team-item"
                >
                  <div class="team-info">
                    <div class="team-name">{{ team.name }}</div>
                    <div class="team-count">{{ team.count }}人</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <Button @click="handleCancel">取消</Button>
        <Button 
          type="primary" 
          @click="handleSubmit" 
          :loading="submitting"
        >
          提交审核
        </Button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TaskSecondAudit',
  data() {
    return {
      taskId: null,
      isEditable: true,
      submitting: false,

      // 任务数据
      taskData: {
        id: '',
        name: '',
        articles: [],
        remark: '',
        reviewerName: '',
        reviewResult: null,
        auditTime: ''
      },

      // 审核结果
      auditResult: 1, // 1: 通过, 2: 不通过

      // 审核数据
      auditData: {
        taskType: '普通',
        executeType: '举报投诉',
        executeTypeChecked: true,
        taskLevel: 1,
        timeType: 1,
        endTime: '',
        reason: '',
        selectedTeamIds: []
      },

      // 队伍树数据
      teamTreeData: [],
      loading: true,

      // 已选中的队伍
      selectedTeams: [],
      totalSelectedCount: 0,

      // 日期选择器配置
      dateOptions: {
        disabledDate: (date) => {
          return date && date.getTime() < Date.now() - 86400000;
        }
      }
    };
  },

  created() {
    this.taskId = this.$route.query.id;
    this.fetchTaskDetail();
    this.fetchTeamTree();
  },

  methods: {
    // 获取任务详情
    fetchTaskDetail() {
      this.$http.get('/linkAge/taskInfo', { params: { id: this.taskId } })
        .then(res => {
          if (res.body.status === 0 && res.body.data) {
            const { taskInfo, articleInfoList } = res.body.data;
            
            // 设置任务数据
            this.taskData = {
              id: taskInfo.id,
              name: taskInfo.name || '',
              remark: taskInfo.remark || '',
              articles: articleInfoList.map(article => ({
                title: article.title || '',
                platform: article.platform || '',
                url: article.url || ''
              })),
              status: taskInfo.status,
              createTime: taskInfo.createTime,
              promoterName: taskInfo.promoterName,
              promoterOrganName: taskInfo.promoterOrganName,
              // 审核相关字段
              reviewerName: taskInfo.reviewerName || '',
              reviewResult: taskInfo.reviewResult,
              auditTime: taskInfo.auditTime || ''
            };

            // 如果任务已经有审核结果，则设置相应的值
            if (taskInfo.taskType) {
              this.auditData.taskType = taskInfo.taskType;
            }

            if (taskInfo.executeType) {
              this.auditData.executeType = taskInfo.executeType;
              this.auditData.executeTypeChecked = taskInfo.executeType === '举报投诉';
            }

            if (taskInfo.taskLevel) {
              this.auditData.taskLevel = parseInt(taskInfo.taskLevel);
            }

            if (taskInfo.timeType) {
              this.auditData.timeType = parseInt(taskInfo.timeType);
            }

            if (taskInfo.endTime) {
              this.auditData.endTime = new Date(taskInfo.endTime);
            }
          } else {
            this.$Message.error(res.body.message || '获取任务详情失败');
          }
        })
        .catch(err => {
          this.$Message.error('获取任务详情失败');
          console.error(err);
        });
    },

    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    },

    // 处理取消
    handleCancel() {
      this.$router.push({
        path: '/main/jointDisposal/taskList'
      });
    },

    // 处理提交
    handleSubmit() {
      if (this.validateForm()) {
        this.submitting = true;

        // 构建提交数据
        const submitData = {
          id: this.taskData.id,
          name: this.taskData.name,
          status: this.auditResult.toString(),
          remark: this.taskData.remark
        };

        if (this.auditResult === 1) {
          // 审核通过
          submitData.taskType = this.auditData.taskType;
          submitData.executeType = this.auditData.executeType;
          submitData.taskLevel = this.auditData.taskLevel.toString();
          submitData.timeType = this.auditData.timeType.toString();

          if (this.auditData.timeType === 6) {
            submitData.endTime = this.auditData.endTime.toISOString();
          }

          submitData.person = this.auditData.selectedTeamIds.join(',');
        } else {
          // 审核不通过
          submitData.reason = this.auditData.reason;
        }

        // 发送审核请求
        this.$http.post('/linkAge/auditTask', submitData, {
          headers: {
            'Content-Type': 'application/json'
          }
        })
          .then(res => {
            if (res.body.status === 0) {
              this.$Message.success('审核操作成功');
              setTimeout(() => {
                this.$router.push({
                  path: '/main/jointDisposal/taskList'
                });
              }, 1000);
            } else {
              this.$Message.error(res.body.message || '审核操作失败');
            }
          })
          .catch(err => {
            this.$Message.error('审核操作失败，请重试');
            console.error(err);
          })
          .finally(() => {
            this.submitting = false;
          });
      }
    },

    // 获取队伍树数据
    fetchTeamTree() {
      this.$http.get('/linkAge/getTeamTree')
        .then(res => {
          if (res.body.status === 0) {
            this.teamTreeData = this.processTreeData(res.body.data || []);
          } else {
            this.$Message.error(res.body.message || '获取队伍数据失败');
          }
        })
        .catch(err => {
          this.$Message.error('获取队伍数据失败');
          console.error(err);
        })
        .finally(() => {
          this.loading = false;
        });
    },

    // 处理树数据
    processTreeData(data) {
      return data.map(item => ({
        title: item.name,
        nodeKey: item.code,
        expand: false,
        checked: false,
        isOrg: item.isOrg,
        origin: item,
        children: item.children ? this.processTreeData(item.children) : []
      }));
    },

    // 处理审核结果变化
    handleAuditResultChange(value) {
      this.auditResult = value;
    },

    // 处理时间类型变化
    handleTimeTypeChange(value) {
      this.auditData.timeType = value;
      if (value !== 6) {
        this.auditData.endTime = '';
      }
    },

    // 处理队伍选择
    handleTeamSelection(checkedNodes, node) {
      const selectedLeafNodes = this.getSelectedLeafNodes(this.teamTreeData);

      this.selectedTeams = selectedLeafNodes
        .filter(node => node.isOrg === 0)
        .map(node => ({
          name: node.title,
          code: node.origin && node.origin.code ? node.origin.code : node.nodeKey,
          count: 1
        }));

      this.totalSelectedCount = this.selectedTeams.reduce((sum, team) => sum + team.count, 0);
      this.auditData.selectedTeamIds = selectedLeafNodes
        .filter(node => node.isOrg === 0)
        .map(node => node.origin && node.origin.code ? node.origin.code : node.nodeKey);
    },

    // 获取选中的叶子节点
    getSelectedLeafNodes(nodes) {
      let selectedNodes = [];

      for (let node of nodes) {
        if (node.checked) {
          if (!node.children || node.children.length === 0) {
            selectedNodes.push(node);
          }
        }

        if (node.children && node.children.length > 0) {
          selectedNodes = selectedNodes.concat(this.getSelectedLeafNodes(node.children));
        }
      }

      return selectedNodes;
    },

    // 表单验证
    validateForm() {
      if (!this.taskData.name.trim()) {
        this.$Message.error('请输入任务名称');
        return false;
      }

      if (this.auditResult === 1) {
        if (this.auditData.timeType === 6) {
          if (!this.auditData.endTime) {
            this.$Message.error('请选择自定义结束时间');
            return false;
          }

          const now = new Date();
          if (this.auditData.endTime < now) {
            this.$Message.error('结束时间不能早于当前时间');
            return false;
          }
        }

        if (this.selectedTeams.length === 0) {
          this.$Message.error('请选择至少一个队伍');
          return false;
        }
      } else {
        if (!this.auditData.reason.trim()) {
          this.$Message.error('请输入未通过原因');
          return false;
        }
      }

      return true;
    }
  }
};
</script>

<style lang="less" scoped>
// 使用与原审核页面相同的样式
@import './taskAudit.vue';
</style>
