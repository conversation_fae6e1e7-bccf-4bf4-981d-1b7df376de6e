<template>
  <div class="task-list flex">
    <div class="tab-container">
      <div class="header flex sb">
        <div class="Toggle flex">
          <div
            :class="['cp', 'flex', 'item', activeTab === 0 ? 'active' : '']"
            @click="switchTab(0)"
          >
            已审核 <span v-if="auditedCount > 0">({{ auditedCount }})</span>
            <div class="triangle"></div>
          </div>
          <div
            :class="['cp', 'flex', 'item', activeTab === 1 ? 'active' : '']"
            @click="switchTab(1)"
            v-if="account.includes(currentAccount)"
          >
            待审核 <span v-if="pendingCount > 0 ">({{ pendingCount }})</span>
            <div class="triangle"></div>
          </div>
        </div>
        <div class="right-controls">
          <Button type="primary" @click="createNewTask">新建任务</Button>
        </div>
      </div>

      <div class="tab-content">
        <!-- 已审核内容 -->
        <div v-show="activeTab === 0" class="tab-pane">
          <div class="task-table" ref="taskTable">
            <Table
              :columns="columns"
              :data="taskList"
              :loading="loading"
              :row-class-name="rowClassName"
              :max-height="tableHeight"
            >
              <template slot-scope="{ row, index }" slot="serialNumber">
                {{ index + 1 }}
              </template>

              <template slot-scope="{ row }" slot="taskName">
                <a @click="viewTaskDetail(row)" class="ellipsis task-name" :title="row.name">{{ row.name }}</a>
              </template>

              <template slot-scope="{ row }" slot="articleCount">
                {{ row.articleNum || 0 }}
              </template>

              <template slot-scope="{ row }" slot="completionRate">
                {{ row.completionRate ? row.completionRate + '%' : '0%' }}
              </template>

              <template slot-scope="{ row }" slot="taskCount">
                {{ row.taskNumber || 0 }}
              </template>

              <template slot-scope="{ row }" slot="completedCount">
                {{ row.completedQuantity || 0 }}
              </template>

              <template slot-scope="{ row }" slot="status">
                <div class="status-dot-container">
                  <div v-if="row.status === 1" class="status-dot blue"></div>
                  <div v-else-if="row.status === 2" class="status-dot red"></div>
                  <div v-else-if="row.status === 3" class="status-dot gray"></div>
                  <div v-else-if="row.status === 4" class="status-dot default"></div>
                  <span v-if="row.status === 1">进行中</span>
                  <span v-else-if="row.status === 2">未通过</span>
                  <span v-else-if="row.status === 3">已结束</span>
                  <span v-else-if="row.status === 4">已转发</span>
                </div>
              </template>

              <template slot-scope="{ row }" slot="issueTime">
                {{ formatDate(row.issuingTime || row.createTime) }}
              </template>

              <template slot-scope="{ row }" slot="promoter">
                {{ row.promoterName }} - {{ row.promoterOrganName }}
              </template>

              <template slot-scope="{ row }" slot="action">
                <div class="action-buttons">
                  <!-- 进行中的任务显示"完结"按钮 -->
                  <Poptip v-if="row.status === 1"
                    confirm
                    placement="bottom"
                    width="300"
                    @on-ok="confirmEndTask(row)"
                    @on-cancel="cancelEndTask">
                    <div slot="title" class="poptip-title">
                      <span>是否完结"{{ row.name }}"任务？</span>
                    </div>
                    <div slot="content" class="poptip-content">
                      <div class="poptip-buttons">
                        <Button type="default" size="small" @click.stop="$refs['endPoptip' + row.id].handleCancel()">取消</Button>
                        <Button type="primary" size="small" @click.stop="confirmEndTask(row)">确定</Button>
                      </div>
                    </div>
                    <a class="action-btn end-btn" ref="endPoptip{{row.id}}">完结</a>
                  </Poptip>

                  <!-- 未转发的任务显示"转发"按钮 -->
                  <Poptip v-if="(row.status === 1 || row.status === 3) && row.isForward === 0"
                    confirm
                    placement="bottom"
                    width="300"
                    @on-ok="confirmForwardTask(row)"
                    @on-cancel="cancelForwardTask">
                    <div slot="title" class="poptip-title">
                      <span>是否将"{{ row.name }}"任务转发至山东通？</span>
                    </div>
                    <div slot="content" class="poptip-content">
                      <div class="poptip-buttons">
                        <Button type="default" size="small" @click.stop="$refs['forwardPoptip' + row.id].handleCancel()">取消</Button>
                        <Button type="primary" size="small" @click.stop="confirmForwardTask(row)">确定</Button>
                      </div>
                    </div>
                    <a class="action-btn forward-btn" ref="forwardPoptip{{row.id}}">转发</a>
                  </Poptip>

                  <!-- 已转发的任务显示"已转发"文本（不可点击） -->
                  <span class="forwarded-text" v-if="row.isForward === 1">已转发</span>

                  <!-- 删除按钮 - 只有指定用户可见 -->
                  <Poptip v-if="account.includes(currentAccount)"
                    confirm
                    placement="bottom"
                    width="180"
                    @on-ok="confirmDeleteTask(row)"
                    @on-cancel="cancelDeleteTask">
                    <div slot="title" class="poptip-title">
                      <span>确认删除任务吗？</span>
                    </div>
                    <div slot="content" class="poptip-content">
                      <div class="poptip-buttons">
                        <Button type="default" size="small" @click.stop="$refs['deletePoptip' + row.id].handleCancel()">取消</Button>
                        <Button type="error" size="small" @click.stop="$refs['deletePoptip' + row.id].handleOk()">删除</Button>
                      </div>
                    </div>
                    <a class="action-btn delete-btn" ref="deletePoptip{{row.id}}">删除</a>
                  </Poptip>

                </div>
              </template>

              <!-- 处理结果列 -->
              <template slot-scope="{ row }" slot="disposalResults">
                <div class="disposal-results-cell">
                  <Select
                    v-model="row.disposalResults"
                    @on-change="updateDisposalResults(row)"
                    placeholder="请选择"
                    style="width: 100%;"
                    size="small"
                  >
                    <!-- <Option value="">请选择</Option> -->
                    <Option value="已删除">已删除</Option>
                    <Option value="已限流">已限流</Option>
                    <Option value="其他">其他</Option>
                  </Select>
                </div>
              </template>

              <!-- 备注列 -->
              <template slot-scope="{ row }" slot="remark">
                <div class="remark-cell">
                  <Tooltip
                    :content="row.remark || '暂无备注'"
                    placement="top"
                    :disabled="!row.remark || row.remark.length <= 10"
                  >
                    <span class="remark-text">
                      {{ row.remark ? (row.remark.length > 10 ? row.remark.substring(0, 10) + '...' : row.remark) : '-' }}
                    </span>
                  </Tooltip>
                </div>
              </template>
            </Table>
          </div>

          <div class="pagination">
            <Page
              :total="total"
              :current="currentPage"
              :page-size="pageSize"
              show-total
              show-elevator
              @on-change="handlePageChange"
            />
          </div>
        </div>

        <!-- 待审核内容 -->
        <div v-show="activeTab === 1" class="tab-pane">
          <div class="task-table" ref="pendingTaskTable">
            <Table
              :columns="pendingColumns"
              :data="pendingTaskList"
              :loading="pendingLoading"
              :row-class-name="rowClassName"
              :max-height="tableHeight"
            >
              <template slot-scope="{ row, index }" slot="serialNumber">
                {{ index + 1 }}
              </template>

              <template slot-scope="{ row }" slot="taskName">
                <a @click="viewTaskDetail(row)" class="ellipsis" :title="row.name">{{ row.name }}</a>
              </template>

              <template slot-scope="{ row }" slot="articleCount">
                {{ row.articleNum || 0 }}
              </template>

              <template slot-scope="{ row }" slot="issueTime">
                {{ formatDate(row.issuingTime || row.createTime) }}
              </template>

              <template slot-scope="{ row }" slot="promoter">
                {{ row.promoterOrganName }}
              </template>

              <template slot-scope="{ row }" slot="promoterName">
                {{ row.promoterName }}
              </template>

              <template slot-scope="{ row }" slot="action">
                <div class="action-buttons">
                  <a class="audit-link" @click="goToAuditPage(row)">审核</a>

                  <!-- 删除按钮 - 只有指定用户可见 -->
                  <Poptip v-if="account.includes(currentAccount)"
                    confirm
                    placement="bottom"
                    width="180"
                    @on-ok="confirmDeleteTask(row)"
                    @on-cancel="cancelDeleteTask">
                    <div slot="title" class="poptip-title">
                      <span>确认删除任务吗？</span>
                    </div>
                    <div slot="content" class="poptip-content">
                      <div class="poptip-buttons">
                        <Button type="default" size="small" @click.stop="$refs['deletePoptip' + row.id].handleCancel()">取消</Button>
                        <Button type="error" size="small" @click.stop="$refs['deletePoptip' + row.id].handleOk()">删除</Button>
                      </div>
                    </div>
                    <a class="action-btn delete-btn" ref="deletePoptip{{row.id}}">删除</a>
                  </Poptip>
                </div>
              </template>
            </Table>
          </div>

          <div class="pagination">
            <Page
              :total="pendingTotal"
              :current="pendingCurrentPage"
              :page-size="pendingPageSize"
              show-total
              show-elevator
              @on-change="handlePendingPageChange"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧筛选 -->
    <TaskFilters @query="handleFilterQuery" :active="activeTab" />

    <Modal
      v-model="disposalResultModalVisible"
      :width="800"
      :closable="false"
    >
      <p
        slot="header"
        style="height: 80px; line-height: 80px; text-align: center;"
      >
        <modalHeader :title="modalTitle" />
      </p>
      <Input
        v-model="disposalResultInput"
        type="textarea"
        :rows="6"
        placeholder="请输入处置结果"
    
      />
      <div slot="footer">
        <div class="foot">
          <span class="inBlock foots" @click="saveDisposalResult">{{ "确定" }}</span>
          <span
            class="inBlock foots"
            style="margin-left: 70px; background: #999999;"
             @click="disposalResultModalVisible = false"
            >{{ "关闭" }}</span
          >
        </div>
      </div>
      <!-- <div style="text-align: center;">
        <Button @click="disposalResultModalVisible = false">关闭</Button>
        <Button type="primary" @click="saveDisposalResult" style="margin-left: 16px;">保存</Button>
      </div> -->
    </Modal>

  </div>
</template>

<script>
import TaskFilters from './components/TaskFilters.vue';
import modalHeader from "@/views/main/publicOpinionTips/components/modalHeader.vue";
export default {
  name: 'TaskList',
  components: {
    TaskFilters,
    modalHeader
  },
  data() {
    return {
      modalTitle: '处置结果',
      tableHeight: 0,
      activeTab: 0, // 0: 已审核, 1: 待审核
      auditedCount: 0,
      pendingCount: 0,
      account:["qianhongguo1","chensu3","chenzhe","weishujie1","dingtong","bijianjun","xupengxiang","test"],
      currentAccount: localStorage.getItem("userAccount"),
      isDataLoading: false, // 标记是否正在加载数据
      // 已审核任务列表相关
      loading: false,
      taskList: [],
      total: 0,
      currentPage: 1,
      pageSize: 20,
      selectedTasks: [],
      queryParams: {}, // 查询参数

      // 待审核任务列表相关
      pendingLoading: false,
      pendingTaskList: [],
      pendingTotal: 0,
      pendingCurrentPage: 1,
      pendingPageSize: 10,
      pendingSelectedTasks: [],
      pendingQueryParams: {}, // 待审核查询参数

      // 弹窗相关 - 不再需要Modal状态

      // 表格列定义
      columns: [
        {
          title: '序号',
          slot: 'serialNumber',
          width: 60,
          align: 'center'
        },
        {
          title: '任务名',
          slot: 'taskName',
          key: 'name',
          width: 200,
          ellipsis: true,
          tooltip: true,
          align: 'left'
        },
        {
          title: '文章数量',
          slot: 'articleCount',
          key: 'articleNum',
          width: 80,
          align: 'center'
        },
        {
          title: '完成率',
          slot: 'completionRate',
          key: 'completionRate',
          width: 70,
          align: 'center'
        },
        {
          title: '任务数',
          slot: 'taskCount',
          key: 'taskNumber',
          width: 70,
          align: 'center'
        },
        {
          title: '完成数',
          slot: 'completedCount',
          key: 'completedQuantity',
          width: 90,
          align: 'center'
        },
        {
          title: '任务状态',
          slot: 'status',
          key: 'status',
          width: 90,
          align: 'center'
        },
        {
          title: '下发时间',
          slot: 'issueTime',
          key: 'issuingTime',
          width: 150,
          align: 'center'
        },
        {
          title: '任务发起人',
          slot: 'promoter',
          key: 'promoterName',
          width: 140,
          align: 'center'
        },
        {
          title: '操作',
          slot: 'action',
          width: 150,
          align: 'center'
        },
        {
          title: '处理结果',
          slot: 'disposalResults',
          width: 90,
          align: 'center'
        },
        {
          title: '备注',
          slot: 'remark',
          width: 180,
          align: 'center'
        }
      ],

      pendingColumns: [
        {
          title: '序号',
          slot: 'serialNumber',
          width: 60,
          align: 'center'
        },
        {
          title: '任务名',
          slot: 'taskName',
          key: 'name',
          minWidth: 200,
          align: 'left'
        },
        {
          title: '文章数量',
          slot: 'articleCount',
          key: 'articleNum',
          width: 80,
          align: 'center'
        },
        {
          title: '任务提交时间',
          slot: 'issueTime',
          key: 'issuingTime',
          width: 150,
          align: 'center'
        },
        {
          title: '申请单位',
          slot: 'promoter',
          key: 'promoterOrganName',
          width: 120,
          align: 'center'
        },
        {
          title: '申请人',
          slot: 'promoterName',
          key: 'promoterName',
          width: 80,
          align: 'center'
        },
        {
          title: '操作',
          slot: 'action',
          width: 130,
          align: 'center'
        }
      ],
      disposalResultModalVisible: false,
      disposalResultInput: '',
      disposalResultRow: null,
    };
  },
  created() {
    // 从URL参数初始化查询参数
    this.initFromUrlParams();

    // 获取任务列表
    this.fetchTaskList();
    this.fetchPendingTaskList();
  },
  mounted() {
    this.$nextTick(() => {
      // 设置一个默认高度，避免 this.$refs.taskTable 为 undefined
      this.tableHeight = this.$refs.taskTable ? this.$refs.taskTable.offsetHeight : 500;

      // 监听窗口大小变化，动态调整表格高度
      window.addEventListener('resize', this.updateTableHeight);
    });
  },

  beforeDestroy() {
    // 组件销毁前移除事件监听
    window.removeEventListener('resize', this.updateTableHeight);
  },
  methods: {
    // 更新表格高度
    updateTableHeight() {
      this.$nextTick(() => {
        // 根据当前激活的标签页选择对应的 ref
        const refName = this.activeTab === 0 ? 'taskTable' : 'pendingTaskTable';
        // 如果 ref 存在则使用其高度，否则使用默认高度
        this.tableHeight = this.$refs[refName] ? this.$refs[refName].offsetHeight : 500;
      });
    },

    // 从URL参数初始化查询参数
    initFromUrlParams() {
      const query = this.$route.query;

      // 如果URL中有参数，使用URL参数
      if (Object.keys(query).length > 0) {
        this.queryParams = { ...query };

        // 处理分页参数
        if (query.pageNo) {
          this.currentPage = parseInt(query.pageNo);
        }

        if (query.pageSize) {
          this.pageSize = parseInt(query.pageSize);
        }

        // 处理标签页参数
        if (query.tab === '1') {
          this.activeTab = 1;
        }
      }
    },

    // 更新URL参数
    updateUrlParams() {
      // 构建新的URL参数
      const urlParams = new URLSearchParams();

      // 添加查询参数
      for (const key in this.queryParams) {
        if (this.queryParams[key]) {
          urlParams.set(key, this.queryParams[key]);
        }
      }

      // 添加分页参数
      urlParams.set('pageNo', this.currentPage.toString());
      urlParams.set('pageSize', this.pageSize.toString());

      // 添加标签页参数
      urlParams.set('tab', this.activeTab.toString());

      // 使用History API更新URL，不触发路由变化
      const newUrl = `${window.location.pathname}${urlParams.toString() ? '?' + urlParams.toString() : ''}`;
      window.history.replaceState(null, '', newUrl);
    },

    // 格式化日期
    formatDate(timestamp) {
      if (!timestamp) return '-';
      const date = new Date(Number(timestamp));
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');

      return `${year}-${month}-${day} ${hours}:${minutes}`;
    },

    // 切换标签页
    switchTab(tabIndex) {
      this.activeTab = tabIndex;

      // 更新URL参数
      this.updateUrlParams();

      if (tabIndex === 0) {
        //已审核
        this.fetchTaskList();
      } else {
        // 待审核
        this.fetchPendingTaskList();
      }

      // 切换标签页后更新表格高度
      this.$nextTick(() => {
        this.updateTableHeight();
      });
    },

    // 创建新任务
    createNewTask() {
      this.$router.push('/main/jointDisposal/createTask');
    },

    // 查看任务详情
    viewTaskDetail(task) {
      this.$router.push(`/main/jointDisposal/taskDetail?id=${task.id}&status=${task.status}`);
    },

    // 处理筛选查询
    handleFilterQuery(params) {
      // 更新查询参数
      this.queryParams = { ...params };

      // 打印查询参数，用于调试
      console.log('筛选查询参数:', this.queryParams);

      // 重置分页
      this.currentPage = 1;

      // 更新URL参数
      this.updateUrlParams();

      // 根据当前标签页获取数据
      if (this.activeTab === 0) {
        this.fetchTaskList();
      } else {
        this.fetchPendingTaskList();
      }
    },

    // 获取已审核任务列表
    fetchTaskList() {
      this.loading = true;
      this.isDataLoading = true; // 设置数据加载标志

      const params = {
        ...this.queryParams,
        pageNo: this.currentPage,
        pageSize: this.pageSize
      };

      // 如果没有指定状态，默认获取已审核状态
      if (params.status==0) {
        params.status = ''; // 已审核状态：进行中、未通过、已结束
      }

      // 打印最终请求参数，用于调试
      console.log('已审核任务列表请求参数:', params);

      this.$http.get('/linkAge/auditTaskList', { params })
        .then(res => {
          if (res.body.status === 0) {
            this.taskList = res.body.data.list || [];
            this.total = res.body.data.count || 0;

            // 获取已审核任务数量
            this.auditedCount = this.total;
          } else {
            this.$Message.error(res.body.message || '获取任务列表失败');
          }
        })
        .catch(err => {
          this.$Message.error('获取任务列表失败');
          console.error(err);
        })
        .finally(() => {
          this.loading = false;
          // 延迟重置标志，确保数据渲染完成
          this.$nextTick(() => {
            setTimeout(() => {
              this.isDataLoading = false;
            }, 100);
          });
        });
    },

    // 获取待审核任务列表
    fetchPendingTaskList() {
      this.pendingLoading = true;
      this.isDataLoading = true; // 设置数据加载标志

      const params = {
        ...this.queryParams,
        status: '0', // 待审核状态
        pageNo: this.pendingCurrentPage,
        pageSize: this.pendingPageSize
      };
      // 修改审核状态
      params.status=0

      // 打印最终请求参数，用于调试
      console.log('待审核任务列表请求参数:', params);

      this.$http.get('/linkAge/auditTaskList', { params })
        .then(res => {
          if (res.body.status === 0) {
            this.pendingTaskList = res.body.data.list || [];
            this.pendingTotal = res.body.data.count || 0;

            // 获取待审核任务数量
            this.pendingCount = this.pendingTotal;
          } else {
            this.$Message.error(res.body.message || '获取待审核任务列表失败');
          }
        })
        .catch(err => {
          this.$Message.error('获取待审核任务列表失败');
          console.error(err);
        })
        .finally(() => {
          this.pendingLoading = false;
          // 延迟重置标志，确保数据渲染完成
          this.$nextTick(() => {
            setTimeout(() => {
              this.isDataLoading = false;
            }, 100);
          });
        });
    },

    // 处理已审核任务列表分页变化
    handlePageChange(page) {
      this.currentPage = page;

      // 更新URL参数
      this.updateUrlParams();

      this.fetchTaskList();
    },

    // 处理待审核任务列表分页变化
    handlePendingPageChange(page) {
      this.pendingCurrentPage = page;

      // 更新URL参数
      this.updateUrlParams();

      this.fetchPendingTaskList();
    },

    // 设置表格行的类名
    rowClassName(_row, index) {
      return (index % 2 === 1) ? 'even-row' : '';
    },

    // 处理完结任务 - 不再需要，直接在Poptip中调用confirmEndTask
    handleEndTask(task) {
      // 保留此方法以便兼容现有代码
      this.confirmEndTask(task);
    },

    // 确认完结任务
    confirmEndTask(task) {
      const params = {
        id: task.id,
        status: 3 // 完结状态
      };

      this.$http.get('/linkAge/taskEnd', { params })
        .then(res => {
          if (res.body.status === 0) {
            this.$Message.success('任务已完结');
            this.fetchTaskList();
          } else {
            this.$Message.error(res.body.message || '完结任务失败');
          }
        })
        .catch(err => {
          this.$Message.error('完结任务失败');
          console.error(err);
        });
    },

    // 取消完结任务
    cancelEndTask() {
      // 不需要做任何操作
    },

    // 处理转发任务 - 不再需要，直接在Poptip中调用confirmForwardTask
    handleForwardTask(task) {
      // 保留此方法以便兼容现有代码
      this.confirmForwardTask(task);
    },

    // 确认转发任务
    confirmForwardTask(task) {
      const params = {
        id: task.id,
        status: 4 // 设置为已转发
      };

      this.$http.get('/linkAge/taskEnd', { params })
        .then(res => {
          if (res.body.status === 0) {
            this.$Message.success('任务已转发至山东通');
            // 更新本地数据，避免重新请求
            const index = this.taskList.findIndex(item => item.id === task.id);
            if (index !== -1) {
              this.taskList[index].isForward = 1;
              // 使用Vue.set确保视图更新
              this.$set(this.taskList, index, {...this.taskList[index]});
            } else {
              // 如果找不到，则重新获取列表
              this.fetchTaskList();
            }
          } else {
            this.$Message.error(res.body.message || '转发任务失败');
          }
        })
        .catch(err => {
          this.$Message.error('转发任务失败');
          console.error(err);
        });
    },

    // 取消转发任务
    cancelForwardTask() {
      // 不需要做任何操作
    },

    // 处理审核通过任务
    handleApproveTask(task) {
      const params = {
        id: task.id,
        status: 1 // 审核通过状态
      };

      this.$http.get('/linkAge/auditTask', { params })
        .then(res => {
          if (res.body.status === 0) {
            this.$Message.success('任务已审核通过');
            this.fetchPendingTaskList();
            this.fetchTaskList();
          } else {
            this.$Message.error(res.body.message || '审核任务失败');
          }
        })
        .catch(err => {
          this.$Message.error('审核任务失败');
          console.error(err);
        });
    },

    // 处理驳回任务
    handleRejectTask(task) {
      const params = {
        id: task.id,
        status: 2 // 驳回状态
      };

      this.$http.get('/linkAge/auditTask', { params })
        .then(res => {
          if (res.body.status === 0) {
            this.$Message.success('任务已驳回');
            this.fetchPendingTaskList();
            this.fetchTaskList();
          } else {
            this.$Message.error(res.body.message || '驳回任务失败');
          }
        })
        .catch(err => {
          this.$Message.error('驳回任务失败');
          console.error(err);
        });
    },

    // 跳转到审核页面
    goToAuditPage(row) {
      const routeData = this.$router.resolve({
        path: '/main/jointDisposal/taskAudit',
        query: { id: row.id }
      });
      window.open(routeData.href, '_blank');
    },

    showDisposalResultModal(row) {
      this.disposalResultRow = row;
      this.disposalResultInput = row.disposalResults || '';
      this.disposalResultModalVisible = true;
    },
    saveDisposalResult() {
      this.$http.get('/linkAge/taskResult', {
        params: {
          id: this.disposalResultRow.id,
          disposalResults: this.disposalResultInput
        }
      }).then(res => {
        if (res.data && res.data.status === 0) {
          this.$Message.success('保存成功');
          this.disposalResultRow.disposalResults = this.disposalResultInput;
          this.disposalResultModalVisible = false;
        } else {
          this.$Message.error(res.data?.message || '保存失败');
        }
      });
    },

    // 更新处理结果
    updateDisposalResults(row) {
      // 如果正在加载数据，不执行更新操作
      if (this.isDataLoading) {
        console.log('数据加载中，跳过处理结果更新');
        return;
      }

      console.log('用户手动更新处理结果:', row.id, row.disposalResults);

      const params = {
        id: row.id,
        disposalResults: row.disposalResults
      };

      this.$http.get('/linkAge/taskResult', { params })
        .then(res => {
          if (res.body && res.body.status === 0) {
            this.$Message.success('处理结果更新成功');
          } else {
            this.$Message.error(res.body?.message || '更新失败');
            // 如果更新失败，恢复原值
            this.$nextTick(() => {
              row.disposalResults = '';
            });
          }
        })
        .catch(err => {
          this.$Message.error('更新失败');
          console.error(err);
          // 如果更新失败，恢复原值
          this.$nextTick(() => {
            row.disposalResults = '';
          });
        });
    },

    // 确认删除任务
    confirmDeleteTask(task) {
      const params = {
        id: task.id
      };

      this.$http.get('/linkAge/deleteTask', { params })
        .then(res => {
          if (res.body.status === 0) {
            this.$Message.success('任务删除成功');
            // 刷新当前列表
            if (this.activeTab === 0) {
              this.fetchTaskList();
            } else {
              this.fetchPendingTaskList();
            }
          } else {
            this.$Message.error(res.body.message || '删除任务失败');
          }
        })
        .catch(err => {
          this.$Message.error('删除任务失败');
          console.error(err);
        });
    },

    // 取消删除任务
    cancelDeleteTask() {
      // 不需要做任何操作
    }
  }
};
</script>

<style lang="less" scoped>
// /deep/.ivu-modal-header .ivu-modal-header-inner{
//   height:60px;
//   line-height:60px;
// }
.foot {
  text-align: center;
  margin-bottom: 18px;
  margin-top: 20px;

  .foots {
    width: 80px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    background: #5585ec;
    border-radius: 4px;
    font-family: PingFang SC;
    font-weight: 600;
    color: #ffffff;
    font-size: 14px;
    cursor: pointer;
  }
}

.inBlock {
  display: inline-block;
}
/deep/ .ivu-modal{
  top:50%;
  margin-top: ~'calc(-260px / 2)';
}
/deep/ .ivu-table-cell{
  font-size:14px;
  padding:0 5px;
}
/deep/.ivu-poptip-footer{
  padding:0px 20px 15px;
  border-top:0;
  button.ivu-btn{
    height:24px;

    font-size:14px;
    padding-top:0;
    padding-bottom:0;
  }
}
/deep/.ivu-poptip-confirm .ivu-poptip-body-message{
  padding-left:0;
}
/deep/.ivu-table-wrapper{
  overflow: visible!important;
}
/deep/.ivu-table-default{
  overflow: visible;
}
/deep/.ivu-table{
  overflow: visible;
}
/deep/.ivu-table-body table{
  width:100%!important
}
/deep/.ivu-table-header table{
  width:100%!important
}
/deep/.ivu-table-cell{
  padding:0 10px;
}
.ellipsis{
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
  display: inline-block;
}
.task-list {
  height: 100%;
  display: flex;

  .tab-container {
    flex: 1;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
    height: 100%;
    display: flex;
    flex-direction: column;
    margin-right: 16px;

    .header {
      margin: 5px 16px;
      border-bottom: 2px solid #2d8cf0;
      align-items: center;


      .Toggle {
        position: relative;

        .item {
          padding: 12px 16px;
          font-size: 14px;
          cursor: pointer;
          position: relative;
          align-items: center;

          &.active {
            color: #2d8cf0;
            font-weight: 500;

            .triangle {
              position: absolute;
              bottom: -1px;
              left: 50%;
              transform: translateX(-50%);
              width: 0;
              height: 0;
              border-left: 6px solid transparent;
              border-right: 6px solid transparent;
              border-bottom: 6px solid #2d8cf0;
            }
          }
        }
      }

      .right-controls {
        display: flex;
        align-items: center;
      }
    }

    .tab-content {
      flex: 1;
      overflow: visible;

      .tab-pane {
        padding: 16px;
        height:100%;
        display: flex;
        flex-direction: column;
        .task-table {
          margin-bottom: 16px;
          flex: 1;
        }

        .pagination {
          display: flex;
          justify-content: center;
          margin-top: 16px;
        }
      }
    }
  }

  .action-buttons {
    button + button {
      margin-left: 8px;
    }

    .audit-link {
      color: #2d8cf0;
      cursor: pointer;
      padding:0 5px;
      &:hover {
        text-decoration: underline;
      }
    }

    .forwarded-text {
      color: #808695; /* 灰色文本，表示已完成的操作 */
      padding:0 5px;
    }
  }
}

// 全局样式
:deep(.ivu-table-wrapper) {
  border-radius: 4px;
  overflow: hidden;
}

:deep(.ivu-table th) {
  background-color: #f8f8f9;
  font-weight: 500;
}

:deep(.ivu-table-row) {
  td {
    height: 48px;
  }
}

:deep(.ivu-tag) {
  border-radius: 2px;
  padding: 0 6px;
  height: 22px;
  line-height: 20px;
}

:deep(.ivu-btn-small) {
  padding: 4px 15px;
  font-size: 14px;
  border-radius: 4px;
}

:deep(.ivu-poptip .ivu-btn-primary) {
  background-color: #2d8cf0;
}

:deep(.ivu-poptip .ivu-btn-default) {
  border-color: #dcdee2;
  color: #515a6e;
}

// 状态点样式
.status-dot-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin-right: 5px;
  display: inline-block;

  &.blue {
    background-color: #2d8cf0;
  }

  &.red {
    background-color: #ed4014;
  }

  &.gray {
    background-color: #808695;
  }

  &.default {
    background-color: #c5c8ce;
  }
}

// 双数行背景色
:deep(.even-row td) {
  background-color: #F4F3F6 !important;
}

// Poptip 样式
:deep(.ivu-poptip-body) {
  padding: 0;
}

:deep(.ivu-poptip-body-content) {
  padding: 0;
}

:deep(.ivu-poptip-popper) {
  min-width: 200px;
}

.poptip-title {
  display: flex;
  align-items: left;
  font-size: 14px;
  font-weight: bold;
  padding: 10px 15px;
  background-color: #fff;
  color: #333;
  position: relative;
  padding-left:40px;
  position: relative;

}

.poptip-title:before {
  content: "!";
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 14px;
  height: 14px;
  border-radius: 50%;
  background-color: #ff9900;
  color: white;
  font-weight: bold;
  margin-right: 10px;
  position: absolute;
  top:13px;
  left:20px;
}

.poptip-title span {
  flex: 1;
  word-break: break-all;
  white-space: normal;
  text-align: left;
}

.poptip-content {
  padding: 10px;
}

.poptip-buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 15px;
}

.action-btn {
  color: #2d8cf0;
  cursor: pointer;
  text-decoration: none;
  // padding: 0 5px;
  &:hover {
    text-decoration: underline;
  }
}

.end-btn, .forward-btn {
  color: #2d8cf0;
}

.delete-btn {
  color: #ed4014;
  // margin-left: 8px;
}

.action-btn.disposal-btn {
  color: #2d8cf0 !important;
  cursor: pointer;
  &:hover {
    text-decoration: underline;
  }
}

/deep/.ivu-tooltip-inner{
  max-width: 400px;
  white-space: normal;
}

// 处理结果和备注列样式
.disposal-results-cell {
  .ivu-select {
    width: 100%;
  }

  .ivu-select-selection {
    border: 1px solid #dcdee2;
    border-radius: 4px;

    &:hover {
      border-color: #57a3f3;
    }
  }

  .ivu-select-focused .ivu-select-selection {
    border-color: #57a3f3;
    box-shadow: 0 0 0 2px rgba(45, 140, 240, 0.2);
  }
}


.remark-cell {
  .remark-text {
    display: inline-block;
    max-width: 100%;
    cursor: pointer;
    color: #515a6e;

    &:hover {
      color: #2d8cf0;
    }
  }
}
</style>
