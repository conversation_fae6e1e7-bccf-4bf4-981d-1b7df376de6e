<template>
    <div class="task-detail">
        <div class="task-info-box">
            <div class="task-info">
                <div class="task-row">
                    <div class="task-row-title">
                        <div class="line"></div>
                        <div class="title">任务信息</div>
                    </div>
                    <div class="task-row-content">
                        <div class="task-row-item">
                            <div class="label">任务名：</div>
                            <div class="value">
                                <span class="bule">{{
                                    taskInfo.name || ""
                                }}</span>
                            </div>
                        </div>
                        <div class="task-row-item">
                            <div class="label">备注：</div>
                            <div class="value">
                                <span class="bule">{{
                                    taskInfo.remark || ""
                                }}</span>
                            </div>
                        </div>
                        <div class="task-row-item">
                            <div class="label">创建时间：</div>
                            <div class="value">
                                <span>{{
                                    taskInfo.createTime | formatDate
                                }}</span>
                            </div>
                        </div>
                        <div class="task-row-item">
                            <div class="label">创建人：</div>
                            <div class="value">
                                <span>{{ taskInfo.promoterOrganName ? taskInfo.promoterOrganName + " - " : "" }}{{ taskInfo.promoterName || "" }}</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="task-row">
                    <div class="task-row-title" v-if="taskStatus && taskStatus != 0">
                        <div class="line"></div>
                        <div class="title">任务审核</div>
                    </div>
                    <div class="task-row-content sh">
                        <div class="task-row-item">
                            <div class="label">审核人：</div>
                            <div class="value">
                                <span>{{
                                    taskInfo.reviewerName || ""
                                }}</span>
                            </div>
                        </div>
                        <div class="task-row-item">
                            <div class="label">审核结果：</div>
                            <div class="value">
                                <span>{{
                                    taskInfo.status !=  2 ? "通过" : "不通过"
                                }}</span>
                                <span v-if="taskInfo.status == 2">
                                   （{{taskInfo.reason || ""}}）
                                </span>
                            </div>
                        </div>
                        <div class="task-row-item">
                            <div class="label">任务等级：</div>
                            <div class="value">
                                <span>{{
                                    taskInfo.taskLevel == 1 ? "高" : taskInfo.taskLevel == 2 ? "中" : "低"
                                }}</span>
                            </div>
                        </div>
                        <div class="task-row-item">
                            <div class="label">审核并下发时间：</div>
                            <div class="value">
                                <span>{{
                                    taskInfo.issuingTime | formatDate
                                }}</span>
                            </div>
                        </div>
                        <div class="task-row-item">
                            <div class="label">任务时间：</div>
                            <div class="value">
                                <span>{{
                                    taskInfo.timeType ? timeTypeMapper[taskInfo.timeType] : ""
                                }}</span>
                            </div>
                        </div>
                        <div class="task-row-item">
                            <div class="label">结束时间：</div>
                            <div class="value">
                                <span>{{
                                    taskInfo.endTime | formatDate
                                }}</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="task-row">
                    <div class="task-row-title">
                        <div class="line"></div>
                        <div class="title">{{ taskStatus == 1 || taskStatus == 3 ? '任务统计' : '任务文章' }}</div>
                    </div>
                    <div class="task-row-content-statistics" v-if="taskStatus == 1 || taskStatus == 3">
                        <div
                            class="task-row-content-statistics-item"
                            v-for="item in articleInfoList"
                            :key="item.id"
                            @click="toggleArticleExpand(item)"
                        >
                            <div class="statistics-info">
                                <div
                                    class="statistics-info-title"
                                    :title="item.articleTitle"
                                >
                                    {{ item.articleTitle }}
                                </div>
                                <div
                                    class="statistics-info-value"
                                    :title="item.url"
                                >
                                    {{ item.url }}
                                </div>
                                <!-- 取证按钮 -->
                                <div class="bbtn" @click.stop="GatherEvidence(item)">
                                    <div v-if="item.isEvidence === 0">
                                    <svg-icon icon-class="重点提示-取证" />
                                    取证
                                    </div>
                                    <div v-if="item.isEvidence === 1">
                                    <svg-icon icon-class="重点提示-取证灰色" />
                                    取证
                                    </div>
                                </div>
                                <div  class="bbtn" @click.stop="LookEvidence(item)">
                                    <svg-icon icon-class="弹框-查看" />
                                    查看取证
                                </div>

                                <div class="arrow-icon" :class="{'arrow-down': item.expanded}">
                                    <Icon type="md-arrow-dropdown" />
                                </div>
                            </div>
                            <div class="statistics-info-remark">
                                <div class="remark-item">
                                    平台：{{ item.app }}
                                </div>
                                <div class="remark-item">
                                    任务状态：{{ item.status }}
                                </div>

                                <!--  v-if="item.startTime" -->
                                <div class="remark-item">
                                    任务开始时间：{{
                                        item.startTime || ""
                                    }}
                                </div>
                                <div class="remark-item">
                                    账号数量：{{ item.wpyCount || "0" }}
                                </div>
                            </div>

                            <!-- 展开的队伍执行情况 -->
                            <div class="team-execution" v-if="item.expanded && item.deptList && item.deptList.length > 0">
                                <div class="team-execution-header">
                                    <div class="team-header-item">序号</div>
                                    <div class="team-header-item">队伍</div>
                                    <div class="team-header-item">账号总数：已完成数</div>
                                    <div class="team-header-item">任务完成度</div>
                                    <div class="team-header-item">操作</div>
                                </div>
                                <div
                                    class="team-execution-row"
                                    v-for="(dept, index) in item.deptList"
                                    :key="dept.deptCode"
                                    :class="{ 'even-row': index % 2 === 1 }"
                                >
                                    <div class="team-row-item">{{ index + 1 }}</div>
                                    <div class="team-row-item">{{ dept.deptCodeName }}</div>
                                    <div class="team-row-item">{{ dept.total }} : {{ dept.finished }}</div>
                                    <div class="team-row-item">{{ (dept.rate * 100).toFixed(1) }}%</div>
                                    <div class="team-row-item">
                                        <a class="detail-link" @click.stop="showTeamDetail(item.articleId, dept.deptCode)">详情</a>
                                    </div>
                                </div>
                            </div>

                            <!-- 加载中状态 -->
                            <div class="team-execution-loading" v-if="item.expanded && item.loading">
                                <Spin>
                                    <Icon type="ios-loading" size="18" class="demo-spin-icon-load"></Icon>
                                    <div>加载中...</div>
                                </Spin>
                            </div>

                            <!-- 无数据状态 -->
                            <div class="team-execution-empty" v-if="item.expanded && !item.loading && (!item.deptList || item.deptList.length === 0)">
                                <div class="empty-text">暂无队伍执行数据</div>
                            </div>
                        </div>
                    </div>
                    <div class="task-row-content-statistics" v-else>
                        <div
                            class="task-row-content-statistics-item"
                            v-for="item in articleInfoList"
                            :key="item.id"
                            @click="toggleArticleExpand(item)"
                        >
                            <div class="statistics-info">
                                <div
                                    class="statistics-info-title"
                                    :title="item.title"
                                >
                                    {{ item.title }}
                                </div>
                                <div
                                    class="statistics-info-value"
                                    :title="item.url"
                                >
                                    {{ item.url }}
                                </div>
                                <!-- 取证按钮 -->
                                <div class="bbtn" @click="GatherEvidence(item)">
                                    <div v-if="item.isEvidence === 0">
                                    <svg-icon icon-class="重点提示-取证" />
                                    取证
                                    </div>
                                    <div v-if="item.isEvidence === 1">
                                    <svg-icon icon-class="重点提示-取证灰色" />
                                    取证
                                    </div>
                                </div>
                                <div  class="bbtn" @click="LookEvidence(item)">
                                    <svg-icon icon-class="弹框-查看" />
                                    查看取证
                                </div>
                                <!-- 在待审核和未通过状态下不显示箭头 -->
                                <div class="arrow-icon" v-if="taskStatus == 1 || taskStatus == 3" :class="{'arrow-down': item.expanded}">
                                    <Icon type="md-arrow-dropdown" />
                                </div>
                            </div>
                            <div class="statistics-info-remark">
                                <div class="remark-item">
                                    平台：{{ item.platform }}
                                </div>
                                <div class="remark-item">
                                    任务状态：{{ item.status }}
                                </div>

                                <!--  v-if="item.startTime" -->
                                <div class="remark-item">
                                    任务开始时间：{{
                                        item.startTime || ""
                                    }}
                                </div>
                                <div class="remark-item">
                                    账号数量：{{ item.accountNum || "0" }}
                                </div>
                                <div class="remark-item">
                                    完成率：{{
                                        item.completionRate || "0" + "%"
                                    }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- <div class="task-row">
                    <div class="task-row-title">
                        <div class="line"></div>
                        <div class="title">处置结果</div>
                        <div class="disposal-result-header">
                            <div  v-if="!isEditingDisposalResult"  @click="startEditDisposalResult">
                                <svg-icon
                                    icon-class="编辑 2"
                                    class="edit-icon"
                                    title="编辑"
                                    style="
                                        width: 24px;
                                        height: 24px;
                                        margin-right: 2px;
                                    " />
                            </div>
                            <div v-else @click="saveDisposalResult">
                                <svg-icon 
                                    icon-class="保存"
                                    class="save-icon"
                                    
                                    title="保存"
                                    style="
                                        width: 24px;
                                        height: 24px;
                                        margin-right: 2px;
                                    " />
                            </div>
                                   
                        
                        </div>
                    </div>
                    <div class="disposal-result-section">
                   
                    <div class="disposal-result-content">
                        <Input
                        v-if="isEditingDisposalResult"
                        v-model="disposalResultInput"
                        type="textarea"
                        :rows="6"
                        placeholder="请输入处置结果"
                        />
                        <div v-else class="disposal-result-text">
                        {{ taskInfo.disposalResults || '暂无处置结果' }}
                        </div>
                    </div>
                    </div>
                </div> -->

                <!-- 队伍详情弹窗 -->
                <Modal v-model="showTeamDetailModal" title="队伍执行详情" width="800">
                    <div class="team-detail-modal">
                        <div class="team-detail-header">
                            <div class="team-detail-header-item">序号</div>
                            <div class="team-detail-header-item">姓名</div>
                            <div class="team-detail-header-item">所属队伍</div>
                            <div class="team-detail-header-item">任务状态</div>
                            <div class="team-detail-header-item">完成时间</div>
                            <div class="team-detail-header-item">任务截图</div>
                        </div>
                        <div class="team-detail-content" v-if="!teamDetailLoading && teamDetailList.length > 0">
                            <div
                                class="team-detail-row"
                                v-for="(item, index) in teamDetailList"
                                :key="index"
                                :class="{ 'even-row': index % 2 === 1 }"
                            >
                                <div class="team-detail-item">{{ index + 1 }}</div>
                                <div class="team-detail-item">{{ item.name }}</div>
                                <div class="team-detail-item">{{ item.belongTeam }}</div>
                                <div class="team-detail-item">
                                    <span :class="getStatusClass(item.status)">{{ item.status }}</span>
                                </div>
                                <div class="team-detail-item">{{ item.time || '-' }}</div>
                                <div class="team-detail-item">
                                    <img
                                        v-if="isValidImageUrl(item.thumbUrl)"
                                        :src="item.thumbUrl"
                                        class="thumb-image"
                                        :alt="item.name + '的任务截图'"
                                        @mouseenter="showHoverPreview($event, item.imageUrl || item.thumbUrl)"
                                        @mouseleave="hideHoverPreview"
                                    />
                                    <span v-else>-</span>
                                </div>
                            </div>
                        </div>
                        <div class="team-detail-loading" v-if="teamDetailLoading">
                            <Spin>
                                <Icon type="ios-loading" size="18" class="demo-spin-icon-load"></Icon>
                                <div>加载中...</div>
                            </Spin>
                        </div>
                        <div class="team-detail-empty" v-if="!teamDetailLoading && teamDetailList.length === 0">
                            <div class="empty-text">暂无队伍执行详情数据</div>
                        </div>
                    </div>
                    <div slot="footer">
                        <Button type="primary" @click="showTeamDetailModal = false">关闭</Button>
                    </div>
                </Modal>

                <!-- 悬停预览 -->
                <div
                    class="hover-preview-container"
                    v-show="hoverPreviewVisible"
                    :style="hoverPreviewStyle"
                >
                    <img
                        :src="hoverPreviewImageUrl"
                        class="hover-preview-image"
                        alt="预览图"
                        @error="hoverPreviewVisible = false"
                    />
                </div>
            </div>
            <div
                class="status-box"
                :class="{
                    'status-wait': taskStatus == 1,
                    'status-audit': taskStatus == 0,
                    'status-audit-fail': taskStatus == 2,
                    'status-end': taskStatus == 3,
                }"
            >
                <!-- 0-待审核 1-进行中 2-审核未通过 3-已结束 -->
                {{
                    taskStatus == 0
                        ? "待审核"
                        : taskStatus == 1
                        ? "进行中"
                        : taskStatus == 2
                        ? "未通过"
                        : taskStatus == 3
                        ? "已结束"
                        : ""
                }}
            </div>
        </div>
        <div class="task-log">
            <taskLog />
        </div>
        <ImgPreview
        v-if="ImgPreviewStatus"
        :imgList="imgList"
        @close="ImgPreviewStatus = false"
        />
    </div>
</template>

<script>
import moment from "moment";
import taskLog from "../components/tasklog.vue";
import ImgPreview from "@/components/imgPreview";
import pathList from "@/assets/js/pathList";

export default {
    name: "TaskDetail",
    data() {
        return {
            pathList,
            taskId: this.$route.query.id,
            taskStatus: this.$route.query.status, // 0-待审核 1-进行中 2-审核未通过 3-已结束
            taskInfo: {},
            articleInfoList: [],
            //   1 1小时内 2 2小时内 3 24小时内 4 48小时内 5 72小时内  6 自定义结束时间
            timeTypeMapper: {
                1: "1小时内",
                2: "2小时内",
                3: "24小时内",
                4: "48小时内",
                5: "72小时内",
                6: "自定义",
            },
            // 队伍详情弹窗相关
            showTeamDetailModal: false,
            teamDetailLoading: false,
            teamDetailList: [],
            currentArticleId: null,
            currentDeptCode: null,

            // 图片预览相关
            showImagePreview: false,
            previewImageUrl: '',

            // 悬停预览相关
            hoverPreviewVisible: false,
            hoverPreviewImageUrl: '',
            hoverPreviewStyle: {
                top: '0px',
                left: '0px'
            },
            isEditingDisposalResult: false,
            disposalResultInput: '',
            ImgPreviewStatus: false,
            imgList: null,
        };
    },
    components: {
        taskLog,
        ImgPreview
    },
    created() {
        this.getTaskInfo();
    },
    methods: {
        //取证
        GatherEvidence(item) { 
        if (item.isEvidence === 1) {
            this.$Message.warning("信息正在取证中，请稍后查看");
            return false;
        }
       
        let params = {
            mkey: item.mkey ? item.mkey.replace("is_evidence_msg_","") : "", //数据mkey
            type: item.type, //1 涉济，2 涉鲁，3涉政
        };

        this.$http
            .post("/recommend/obtainEvidenceMsg", params, { emulateJSON: true })
            .then((res) => {
            if (res.body.status === 0) {
                this.$Message.warning("信息正在取证中，请稍后查看");
                item.isEvidence = 1;
            } else {
                this.$Message.error(res.body.message);
            }
            });
        },
        // 查看取证
        LookEvidence(item) {
            console.log(item);
            const LookEvidence = () => import("@/components/LookEvidence");
        
            const sys_log_module = this.$route.query.moduleName
            ? decodeURIComponent(this.$route.query.moduleName)
            : this.$route.meta.moduleName;
            console.log("aaa",sys_log_module)
            this.getLog(
            sys_log_module,
            "查看取证/" + item.title,
            item.mkey ? item.mkey.replace("is_evidence_msg_","") : ""
            );
            // 处理下数据
            let data =item;
            data.mkey=item.mkey.replace("is_evidence_msg_","")
        this.$modal.show({
            component: LookEvidence,
            componentProps: {
            data: item,
            that: this,
            pathList: this.pathList,
            },
            componentEvents: {
            // closes: this.tipCancelModel,
            blowUp: this.blowUp,
            },
            title: "查看取证", // 传递标题
            // y: 300,
        });
        },
        //图片放大
        blowUp(imgList) {
        this.imgList = imgList;
        this.ImgPreviewStatus = true;
        },
        // 获取任务信息
        getTaskInfo() {
            let url = "/linkAge/taskInfo";
            if(this.taskStatus == 1 || this.taskStatus == 3){
                url = "/linkAge/taskInfoDetail";
            }
            this.$http
                .get(url, {
                    params: {
                        id: this.taskId,
                    },
                })
                .then((res) => {
                    this.taskInfo = res.body.data.taskInfo;
                    this.articleInfoList = res.body.data.articleInfoList || [];

                    // 初始化每个文章项的展开状态
                    this.articleInfoList.forEach(item => {
                        this.$set(item, 'expanded', false);
                        this.$set(item, 'loading', false);
                        this.$set(item, 'deptList', []);
                        //设置取证初始化
                        this.$set(item, 'isEvidence', item.isEvidence?item.isEvidence:0);
                    });

                    console.log(this.articleInfoList);
                });
        },

        // 切换文章项的展开/收起状态
        toggleArticleExpand(item) {
            // 只有在进行中或已结束状态下才允许展开和加载数据
            if (this.taskStatus == 1 || this.taskStatus == 3) {
                // 如果当前是收起状态，则展开并加载数据
                if (!item.expanded) {
                    this.$set(item, 'expanded', true);
                    this.loadArticleDeptList(item);
                } else {
                    // 如果当前是展开状态，则收起
                    this.$set(item, 'expanded', false);
                }
            }
            // 在待审核和未通过状态下不执行任何操作
        },

        // 加载文章的部门列表数据
        loadArticleDeptList(item) {
            this.$set(item, 'loading', true);

            this.$http.get('/linkAge/getArticleList', {
                params: {
                    articleId: item.articleId
                }
            })
            .then(res => {
                if (res.body.status === 1) {
                    this.$set(item, 'deptList', res.body.body.data || []);
                } else {
                    this.$Message.error(res.body.msg || '获取队伍执行情况失败');
                    this.$set(item, 'deptList', []);
                }
            })
            .catch(err => {
                console.error('获取队伍执行情况失败', err);
                this.$Message.error('获取队伍执行情况失败');
                this.$set(item, 'deptList', []);
            })
            .finally(() => {
                this.$set(item, 'loading', false);
            });
        },

        // 显示队伍详情
        showTeamDetail(articleId, deptCode) {
            this.currentArticleId = articleId;
            this.currentDeptCode = deptCode;
            this.showTeamDetailModal = true;
            this.teamDetailList = [];
            this.loadTeamDetailList();
        },

        // 加载队伍详情列表
        loadTeamDetailList() {
            this.teamDetailLoading = true;

            this.$http.get('/linkAge/getArticleDeptList', {
                params: {
                    articleId: this.currentArticleId,
                    deptCode: this.currentDeptCode
                }
            })
            .then(res => {
                if (res.body.status === 1) {
                    this.teamDetailList = res.body.body.data || [];
                } else {
                    this.$Message.error(res.body.msg || '获取队伍执行详情失败');
                    this.teamDetailList = [];
                }
            })
            .catch(err => {
                console.error('获取队伍执行详情失败', err);
                this.$Message.error('获取队伍执行详情失败');
                this.teamDetailList = [];
            })
            .finally(() => {
                this.teamDetailLoading = false;
            });
        },

        // 获取任务状态的样式类
        getStatusClass(status) {
            if (status === '已完成') return 'status-completed';
            if (status === '未执行') return 'status-pending';
            return '';
        },

        // 显示悬停预览
        showHoverPreview(event, imageUrl) {
            // 如果图片URL无效，不显示预览
            if (!this.isValidImageUrl(imageUrl)) {
                return;
            }

            // 获取缩略图的位置和尺寸（当前未使用）
            // const thumbRect = event.target.getBoundingClientRect();

            // 获取弹窗的位置和尺寸
            const modalRect = document.querySelector('.ivu-modal-content').getBoundingClientRect();

            // 计算预览图片的位置
            // 将预览图片放在弹窗的右侧
            const leftPosition = modalRect.right + 10; // 弹窗右边缘 + 10px间距

            // 计算预览图片的顶部位置，与点击的缩略图垂直对齐
            let topPosition = 130;

            // 确保预览图片不会超出视口顶部
            if (topPosition < 10) {
                topPosition = 10;
            }

            // 确保预览图片不会超出视口底部
            const viewportHeight = window.innerHeight;
            if (topPosition + 400 > viewportHeight - 10) { // 假设预览图片高度最大为400px
                topPosition = viewportHeight - 400 - 10;
            }

            // 设置预览图片的位置和URL
            this.hoverPreviewStyle = {
                top: `${topPosition}px`,
                left: `${leftPosition}px`
            };
            this.hoverPreviewImageUrl = imageUrl;
            this.hoverPreviewVisible = true;
        },

        // 隐藏悬停预览
        hideHoverPreview() {
            this.hoverPreviewVisible = false;
        },

        // 检查图片URL是否有效
        isValidImageUrl(url) {
            // 检查 url 是否为 null 或 undefined
            if (url === null || url === undefined) {
                return false;
            }

            // 检查 url 是否为字符串类型
            if (typeof url !== 'string') {
                return false;
            }

            // 检查 url 是否为空字符串或只包含空格
            if (url.trim() === '') {
                return false;
            }

            // 检查 url 是否包含 'null' 或 'undefined' 字符串
            if (url === 'null' || url === 'undefined' || url.includes('null') || url.includes('undefined')) {
                return false;
            }

            return true;
        },
        startEditDisposalResult() {
            this.isEditingDisposalResult = true;
            this.disposalResultInput = this.taskInfo.disposalResults || '';
        },
        async saveDisposalResult() {
            if (!this.disposalResultInput.trim()) {
                this.$Message.warning('请输入处置结果');
                return;
            }
            const res = await this.$http.get('/linkAge/taskResult', {
                params: {
                    id: this.taskId,
                    disposalResults: this.disposalResultInput
                }
            });
            if (res.data && res.data.status === 0) {
                this.$Message.success('保存成功');
                this.taskInfo.disposalResults = this.disposalResultInput;
                this.isEditingDisposalResult = false;
                if (typeof this.$refs.taskLog?.refresh === 'function') {
                    this.$refs.taskLog.refresh();
                }
            } else {
                this.$Message.error(res.data?.message || '保存失败');
            }
        },
    },
    filters: {
        formatDate(date) {
            if (!date) return "";
            return moment(Number(date)).format("YYYY-MM-DD HH:mm:ss");
        },
    },
};
</script>

<style lang="less" scoped>
.task-detail {
    height: 100%;
    overflow: hidden;
    display: flex;
    gap: 20px;
    .task-info-box {
        flex: 1;
        overflow: hidden;
        width: 1350px;
        background-color: #fff;
        border-radius: 4px;
        padding: 20px;
        position: relative;
        .status-box {
            position: absolute;
            top: 10px;
            right: 10px;
            width: 60px;
            height: 30px;
            line-height: 30px;
            text-align: center;
            border-radius: 4px;
            background-color: #5378e5;
            color: #fff;
            font-size: 14px;
            &.status-wait {
                background-color: #4ca5ff;
            }
            &.status-audit {
                background-color: #f39200;
            }
            &.status-audit-fail {
                background-color: #ff0000;
            }
            &.status-end {
                background-color: #797979;
            }
        }
    }
    .task-info {
        height: 100%;
        overflow-y: auto;
        .task-row {
            &:not(:last-child) {
                margin-bottom: 30px;
            }
            .task-row-title {
                display: flex;
                align-items: center;
                height: 40px;
                margin-bottom: 20px;
                .line {
                    width: 6px;
                    height: 16px;
                    background: #5378e5;
                    margin-right: 10px;
                }
                .title {
                    font-weight: 600;
                    color: #333;
                    font-size: 20px;
                }
            }
            .task-row-content {
                display: flex;
                flex-wrap: wrap;
                // align-items: center;
                justify-content: space-between;
                gap: 20px;
                &.sh{
                    .task-row-item{
                        width: 32%;
                    }
                }
                .task-row-item {
                    width: 48%;
                    flex-shrink: 0;
                    overflow: hidden;
                    display: flex;
                    font-size: 14px;
                    line-height: 20px;
                    color: #333;
                    .blue {
                        color: #6b9ad4;
                    }
                    .value {
                        flex: 1;
                        overflow: hidden;
                    }
                }
            }
            .task-row-content-statistics {
                display: flex;
                flex-direction: column;
                gap: 20px;
                .task-row-content-statistics-item {
                    width: 100%;
                    padding: 20px;
                    border-radius: 6px;
                    background-color: #dfe9fc;
                    font-size: 14px;
                    line-height: 20px;
                    cursor: pointer;
                    transition: all 0.3s;

                    &:hover {
                        background-color: #d0e0fa;
                    }

                    .statistics-info {
                        display: flex;
                        align-items: center;
                        gap: 10px;
                        position: relative;
                        padding-right:30px;
                        .statistics-info-title {
                            font-weight: bold;
                            max-width: 65%;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                        }
                        .statistics-info-value {
                          
                            color: #6b8aea;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                            flex:1;
                        }
                        .arrow-icon {
                            position: absolute;
                            right: 0;
                            top: 50%;
                            transform: translateY(-50%);
                            transition: all 0.3s;

                            .ivu-icon {
                                font-size: 24px;
                                color: #5378e5;
                            }

                            &.arrow-down {
                                transform: translateY(-50%) rotate(180deg);
                            }
                        }
                        .bbtn{
                            border:1px solid #5378e5;
                            border-radius:4px;
                            padding:0 5px;
                        }
                    }
                    .statistics-info-remark {
                        margin-top: 10px;
                        display: flex;
                        gap: 20px;
                    }

                    // 队伍执行情况样式
                    .team-execution {
                        margin-top: 20px;

                        padding-top: 15px;
                        background:white;
                        padding:20px;

                        .team-execution-header {
                            display: flex;
                            background-color: #f0f5ff;
                            padding: 10px 0;
                            font-weight: bold;
                            border-radius: 4px;

                            .team-header-item {
                                flex: 1;
                                text-align: center;

                                &:first-child {
                                    flex: 0.5;
                                }

                                &:last-child {
                                    flex: 0.8;
                                }
                            }
                        }

                        .team-execution-row {
                            display: flex;
                            padding: 12px 0;
                            border-bottom: 1px solid #eee;

                            &.even-row {
                                background-color: #F4F3F6;
                            }

                            .team-row-item {
                                flex: 1;
                                text-align: center;

                                &:first-child {
                                    flex: 0.5;
                                }

                                &:last-child {
                                    flex: 0.8;
                                }

                                .detail-link {
                                    color: #2d8cf0;
                                    cursor: pointer;

                                    &:hover {
                                        text-decoration: underline;
                                    }
                                }
                            }
                        }
                    }

                    .team-execution-loading, .team-execution-empty {
                        margin-top: 20px;
                        padding: 30px 0;
                        text-align: center;
                        border-top: 1px solid #c5d3f3;
                    }

                    .empty-text {
                        color: #999;
                        font-size: 14px;
                    }
                }
            }
        }
    }
    .task-log {
        width: 370px;
        flex-shrink: 0;
        overflow-y: auto;
    }
}

// 队伍详情弹窗样式
.team-detail-modal {
    .team-detail-header {
        display: flex;
        background-color: #f0f5ff;
        padding: 10px 0;
        font-weight: bold;

        .team-detail-header-item {
            flex: 1;
            text-align: center;

            &:first-child {
                flex: 0.5;
            }

            &:last-child {
                flex: 1.5;
            }
        }
    }

    .team-detail-content {
        max-height: 400px;
        overflow-y: auto;

        .team-detail-row {
            display: flex;
            padding: 12px 0;
            border-bottom: 1px solid #eee;
            align-items: center;

            &.even-row {
                background-color: #F4F3F6;
            }

            .team-detail-item {
                flex: 1;
                text-align: center;

                &:first-child {
                    flex: 0.5;
                }

                &:last-child {
                    flex: 1.5;
                }

                .status-completed {
                    color: #19be6b;
                }

                .status-pending {
                    color: #ff9900;
                }

                .thumb-image {
                    width: 80px;
                    height: 60px;
                    object-fit: cover;
                    border-radius: 4px;
                    cursor: pointer;
                    transition: all 0.3s;

                    &:hover {
                        transform: scale(1.05);
                    }
                }
            }
        }
    }

    .team-detail-loading, .team-detail-empty {
        padding: 40px 0;
        text-align: center;
    }
}

// 悬停预览样式
.hover-preview-container {
    position: fixed;
    z-index: 1000;
    padding: 10px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
    pointer-events: none; // 防止预览图片阻挡鼠标事件
    border: 1px solid #e8e8e8;

    .hover-preview-image {
        max-width: 300px; // 设置最大宽度为300px
        max-height: 400px;
        object-fit: contain;
        display: block; // 确保图片正确显示
        border-radius: 4px;
    }

    // 移除小三角形指示器
}

// 修复demo-spin-icon-load样式
.demo-spin-icon-load {
    animation: ani-demo-spin 1s linear infinite;
}

@keyframes ani-demo-spin {
    from { transform: rotate(0deg); }
    50% { transform: rotate(180deg); }
    to { transform: rotate(360deg); }
}

.disposal-result-section {

//   border: 1px solid #e8e8e8;
//   border-radius: 6px;
//   padding: 16px;
//   background: #f8faff;
}
.disposal-result-header {
    flex:1;
    
  display: flex;
  align-items: end;
  font-weight: bold;
  font-size: 16px;

  justify-content: end;
  .edit-icon, .save-icon {
    margin-left: 8px;
    color: #2d8cf0;
    cursor: pointer;
    font-size: 20px;
  }
}
.disposal-result-content {
  .disposal-result-text {
    min-height: 80px;
    color: #333;
    font-size: 15px;
    background: #fff;
    border-radius: 4px;
    padding: 12px;
    border: 1px solid #e8e8e8;
    background: #ededed;
  }
}
</style>
