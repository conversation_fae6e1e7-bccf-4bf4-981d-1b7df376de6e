<template>
  <div class="filter">
    <div class="header flex sb">
      <div class="title">筛选</div>
      <div class="btn flex">
        <div class="item cp" @click="screen">查询</div>
        <!-- <div class="item save cp" @click="openModel">筛选并保存</div> -->
      </div>
    </div>
    <div class="conditions">
      <div class="item" v-for="item in zongRule" :key="item.key">
        <div class="label flex">
          <div class="line"></div>
          <div class="title">{{ item.name }}</div>
        </div>
        <div class="options flex" v-if="!item.type">
          <div
            :class="[
              'item',
              'cp',
              item.check
                ? params[item.key].includes(k)
                  ? 'active'
                  : ''
                : params[item.key] === k
                ? 'active'
                : '',
            ]"
            v-for="(v, k) in item.options"
            :key="k"
            @click="selectData(item, k)"
          >
            {{ v }}
          </div>
          <DatePicker
            v-if="item.key === 'dayNum' && params.dayNum === '99'"
            type="datetimerange"
            placeholder="Select date and time"
            style="width: 240px"
            transfer
            @on-change="dateChange"
          ></DatePicker>
        </div>
        <div class="options flex" v-if="item.type && item.type == 'input'">
            <Input v-model="params[item.key]" placeholder="" style="width: 280px;" />
        </div>
        <div class="options flex" v-if="item.type && item.type == 'select'">
            <Select v-model="params[item.key]" style="width:280px">
                <Option :value="0"  >全部</Option>
                <Option v-for="(opItem, opIndex) in item.options" :value="opItem" :key="opIndex">{{ opItem }}</Option>
            </Select>
        </div>
        <div class="options flex" v-if="item.type && item.type == 'selectKey'">
            <Select v-model="params[item.key]" style="width:280px">
                <Option :value="0" >全部</Option>
                <Option v-for="(opItem, opIndex) in item.options" :value="opItem.value" :key="opIndex">{{ opItem.name }}</Option>
            </Select>
        </div>
        <div class="options flex" v-if="item.type && item.type == 'selectHasY'">
            <Select v-model="params[item.key]" style="width:280px">
                <Option :value="3" >全部</Option>
                <Option v-for="(opItem, opIndex) in item.options" :value="opItem.value" :key="opIndex">{{ opItem.name }}</Option>
            </Select>
        </div>
        <div class="options flex" v-if="item.type && item.type == 'selectSource'">
            <Select v-model="params[item.key]" style="width:280px">
                <Option :value="3" >全部</Option>
                <Option v-for="(opItem, opIndex) in item.options" :value="opItem.value" :key="opIndex">{{ opItem.name }}</Option>
            </Select>
        </div>
        <div class="options flex" v-if="item.type && item.type == 'selectTypeNum'">
            <Select v-model="params[item.key]" style="width:280px">
                <Option :value="0" >全部</Option>
                <Option v-for="(opItem, opIndex) in item.options" :value="opItem.value" :key="opIndex">{{ opItem.name }}</Option>
            </Select>
        </div>
        <div class="options flex" v-if="item.type && item.type == 'selectHasXinXi'">
            <Select v-model="params[item.key]" style="width:280px" >
                <Option value="" >请选择</Option>
                <Option v-for="(opItem, opIndex) in item.options" :value="opItem.value" :key="opIndex">{{ opItem.name }}</Option>
            </Select>
        </div>
        <div class="options flex" v-if="item.type && item.type == 'selectManualCompletion'">
            <Select v-model="params[item.key]" style="width:280px" placeholder="请选择">
                <Option value="" >请选择</Option>
                <Option v-for="(opItem, opIndex) in item.options" :value="opItem.value" :key="opIndex">{{ opItem.name }}</Option>
            </Select>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具 js，第三方插件 js，json 文件，图片文件等等）
// 例如：import  《组件名称》  from '《组件路径》 ';
import { dayNum, } from "./options";
import situations from "@/assets/json/platform.json";
import moment from "moment/moment";
import { allRule } from "./options";


const typeList = {
  1: "新增",
  2: "替换",
};

export default {
  data() {
    // 这里存放数据
    return {
      moment,
      allRule,
      zongRule:[],
      dayNum,
      situations,
      params: {
        situations: ["0"],
        accountName: '',
        register: 0,
        level: 0,
        hasXinXi: 1,
        manualCompletion: 0,
        contactSource: ["0"],
        source: 3,
        typeNum: 0
      },

    };
  },
  // import 引入的组件需要注入到对象中才能使用
  components: {  },
  props: {



  },
  // 生命周期 - 创建完成（可以访问当前this 实例）
  created() {
    // 判断页面是selfMediaLibrary的话，allRule中添加关联信息
    if (this.$route.path.includes("selfMediaLibrary")) {
      // 检查allRule中是否已经存在key为isAssociated的项
      const hasAssociatedRule = this.allRule.some(rule => rule.key === 'isAssociated');

      // 使用$set确保响应式更新
      this.$set(this.params, 'isAssociated', 0);
    }

    // 添加对isAssociated的特殊处理
    // this.$watch('params.isAssociated', (newVal, oldVal) => {
    //     // 如果值应该是0但不是0，强制设置为0
    //     if (newVal === 0 && this.params.isAssociated !== 0) {
    //         this.$set(this.params, 'isAssociated', 0);
    //     }
    // });

    this.unfoldScreen();
  },
  // 方法集合
  methods: {
    screen() {
      this.unfoldScreen();
    },
    unfoldScreen() {
      let params = {
        situation: this.params.situations.toString(),
        accountName: this.params.accountName,
        register: this.params.register === 0 ? '' : this.params.register,
        level: this.params.level === 0 ? '' : this.params.level,
        hasXinXi: this.params.hasXinXi,
        manualCompletion: this.params.manualCompletion === 0 ? '' : this.params.manualCompletion,
        contactSource: this.params.contactSource.includes("0") ? '' : this.params.contactSource.filter(item => item !== "0").join(','),
        source: this.params.source === 3 ? '' : this.params.source,
        typeNum: this.params.typeNum === 0 ? '' : this.params.typeNum
      };
      if (this.$route.path.includes("selfMediaLibrary")) {
        params.isAssociated = this.params.isAssociated === 0 ? '' : this.params.isAssociated;
      }
      Object.keys(params).forEach((i) => {
        if (params[i] === "0") {
          params[i] = null;
        }
      });
      this.$emit("query", {
        ...params,
      });
    },

    dateChange(d) {
      this.params["startTime"] = d[0];
      this.params["endTime"] = d[1];
    },
    reset() {
      this.params = {
        situations: ["0"],
        accountName: '',
        register: 0,
        level: 0,
        hasXinXi: 1,
        manualCompletion: 0,
        contactSource: ["0"],
        source: 3,
        typeNum: 0
      };
      if (this.$route.path.includes("selfMediaLibrary")) {
        this.params.isAssociated = 0;
      }
    },
    selectData(data, k) {
      if (data.check) {
        //多选
        // 如果当前选项是"0" (全部)，那直接重置为['0']
        if (k === "0") {
          this.params[data.key] = [k];
          return;
        }
        if (this.params[data.key].includes(k)) {
          // 有值就删掉
          this.params[data.key].splice(this.params[data.key].indexOf(k), 1);
          if (this.params[data.key].length === 0) {
            this.params[data.key] = ["0"];
          }
        } else {
          // 添加前先判断有没有全部选项，有的话去掉
          if (this.params[data.key].includes("0")) {
            // 有值就删掉
            this.params[data.key].splice(this.params[data.key].indexOf("0"), 1);
          }
          // 没值就添加
          this.params[data.key].push(k);
        }
      } else {
        //单选
        this.params[data.key] = k;
      }
    },
  },

  watch: {
    // defaultScreen(d) {
    //   if (d) {
    //     let json = JSON.parse(d);
    //     this.params = {
    //       ...json,
    //       situations: json.situations ? json.situations.split(",") : ["0"],
    //       regionals: json.regionals ? json.regionals.split(",") : ["0"],
    //       dayNum: json.dayNum ? json.dayNum : "1",
    //     };
    //     this.fields = json.fields
    //       ? json.fields.split(",").map((i) => Number(i))
    //       : [0];
    //     // this.$parent.defaultScreen = "";
    //     this.unfoldScreen();
    //   }
    // },
  },
  //过滤器
  filters: {},

  // 生命周期 - 挂载完成（可以访问 DOM 元素）
  mounted() {
    this.zongRule = this.allRule;
  },
};
</script>

<style scoped lang="less">
.filter {
  width: 340px;
  background-color: #fff;
  border-radius: 8px;
  height: 100%;
  padding: 10px 8px;

  .header {
    align-items: flex-end;

    .title {
      font-family: PingFang SC;
      font-weight: 600;
      color: #333333;
      font-size: 16px;
      line-height: 16px;
    }

    .btn {
      .item {
        text-align: center;
        line-height: 30px;
        font-weight: 600;
        color: #ffffff;
        font-size: 14px;
        width: 80px;
        height: 30px;
        border-radius: 4px;
        background: #5585ec;
        margin-left: 10px;
      }

      .save {
        background: #f4a000;
      }
    }
  }

  .conditions {
    height: calc(~"100% - 50px");
    overflow-y: auto;
    /deep/ .ivu-date-picker {
      .ivu-input-inner-container,
      .ivu-input-suffix {
        margin-top: 8px;
      }

      .ivu-input {
        height: 22px;
      }
    }

    & > .item {
      margin-top: 20px;
      position: relative;

      .yuanBtn {
        position: absolute;
        right: 30px;
        top: 0;

        /deep/ .ivu-radio-wrapper {
          font-size: 14px;
        }
      }

      .label {
        align-items: center;

        .line {
          width: 3px;
          height: 12px;
          background: #537be6;
          margin-right: 5px;
        }

        .title {
          color: #333333;
          font-size: 14px;
          line-height: 30px;
        }
      }

      .options {
        flex-wrap: wrap;

        .item {
          margin-top: 10px;
          margin-right: 10px;
          padding: 0 5px;
          height: 22px;
          border: 1px solid #c4c3c3;
          border-radius: 2px;
          line-height: 20px;
          text-align: center;
          font-size: 14px;
        }

        .active {
          border-color: #537be6;
          color: #537be6;
        }
      }
    }
  }
}

.frame {
  & > .label {
    background-color: #eee;
    height: 30px;

    .item {
      line-height: 30px;
      padding: 0 10px;
    }

    .active {
      background-color: #537be6;
      color: #fff;
    }
  }

  & > .content {
    padding: 0 20px;
    width: 350px;

    .list {
      //padding: 10px;
      flex-wrap: wrap;

      .item {
        line-height: 30px;
        padding: 0 10px;
        background-color: #eee;
        margin: 2px 5px;
        cursor: pointer;
      }

      .active {
        background-color: #537be6;
        color: #fff;
      }
    }
  }
}
.box {
  display: flex;
  justify-content: space-between;
  padding: 0 30%;

  .modalBtn {
    cursor: pointer;
    line-height: 40px;
    text-align: center;
    width: 80px;
    height: 40px;
    background: #5585ec;
    border-radius: 4px;
    font-weight: 600;
    color: #ffffff;
    font-size: 14px;
    &:nth-child(2) {
      background: #999;
    }
  }
}
</style>