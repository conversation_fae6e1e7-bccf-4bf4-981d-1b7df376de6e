import situations from "@/assets/json/platform.json";
import registerOption from './registerOption'
const dayNum = {
  0: "全部",
  1: "近一日",
  2: "今日",
  3: "近三日",
  7: "近一周",
  30: "近一月",
  99: "自定义",
};


const allRule = [
//   {
//     name: "时间",
//     key: "dayNum",
//     options: dayNum,
//   },

  {
    name: "平台",
    key: "situations",
    check: true,
    options: situations,
  },
  {
    name: "帐号昵称",
    key: "accountName",
    type: 'input',
  },
  {
    name: "注册地",
    key: "register",
    type: 'select',
    options: registerOption
  },
  {
    name: "账号等级",
    key: "level",
    type: 'selectKey',
    options: [
        {
            name: '一级',
            value: 1
        },
        {
            name: '二级',
            value: 2
        },
        {
            name: '三级',
            value: 3
        }
    ]
  },
  {
        name: "人物信息",
        key: "hasXinXi",
        type: 'selectHasXinXi',
        options: [
            {
                name: '有联系方式',
                value: 1
            },
            {
                name: '无联系方式但有人物信息',
                value: 2
            },
            {
                name: '人物信息全部为空',
                value: 3
            }
        ]
  },
  {
        name: "人工完善情况",
        key: "manualCompletion",
        type: 'selectManualCompletion',
        options: [
            {
                name: '完善任何信息',
                value: 1
            },
            {
                name: '完善联系方式',
                value: 2
            },
            {
                name: '完善除联系方式外其他信息',
                value: 3
            }
        ]
  },
  {
        name: "联系方式来源",
        key: "contactSource",
        check: true,
        options: {
            "0": "全部",
            "1": "市办用户",
            "2": "区县用户",
            "3": "系统获取"
        }
  },
  {
          name: "账号来源",
          key: "source",
          type: 'selectSource',
          options: [
              {
                  name: '系统更新',
                  value: 0
              },
              {
                  name: '三方提供',
                  value: 1
              },
              {
                  name: '用户添加',
                  value: 2
              }
          ]
  },
  {
            name: "处置方式",
            key: "typeNum",
            type: 'selectTypeNum',
            options: [
                {
                    name: '删除',
                    value: 1
                },
                {
                    name: '打标',
                    value: 2
                },
                {
                    name: '提示单引用',
                    value: 3
                },
                {
                    name: '要报引用',
                    value: 4
                }
            ]
  }
 
];

export { dayNum, allRule};
