<template>
  <div class="listBox">
    <div class="header flex">
      <div class="item" style="width: 70px;">序号</div>
      <div
        class="item"
        style="
          width: 175px;
          text-align: left;
          justify-content: flex-start;
        "
      >
        账号昵称
      </div>
      <div class="item" style="width: 140px;">所属平台</div>
      <div class="item" style="width: 100px;">账号ID</div>
      <div
        class="item"
        style="width: 140px;"
        @click="
          orderByClick(
            'fans_count',
            orderBy == 'fans_count'
              ? orderByType == 'asc'
                ? 'desc'
                : 'asc'
              : 'asc'
          )
        "
      >
        <span>粉丝数</span>
        <svg-icon
          v-show="orderBy == 'fans_count' && orderByType == 'asc'"
          icon-class="哨点-升序"
          style="width: 6.87px; height: 11.95px; margin-left: 8px;"
        />
        <svg-icon
          v-show="orderBy == 'fans_count' && orderByType == 'desc'"
          icon-class="哨点-降序"
          style="width: 6.87px; height: 11.95px; margin-left: 8px;"
        />
      </div>
      <div
        class="item"
        style="width: 140px;"
        @click="
          orderByClick(
            'push_num',
            orderBy == 'push_num'
              ? orderByType == 'asc'
                ? 'desc'
                : 'asc'
              : 'asc'
          )
        "
      >
        <span>涉济发文总量</span>
        <svg-icon
          v-show="orderBy == 'push_num' && orderByType == 'asc'"
          icon-class="哨点-升序"
          style="width: 6.87px; height: 11.95px; margin-left: 8px;"
        />
        <svg-icon
          v-show="orderBy == 'push_num' && orderByType == 'desc'"
          icon-class="哨点-降序"
          style="width: 6.87px; height: 11.95px; margin-left: 8px;"
        />
      </div>
      <div
        class="item"
        style="width: 140px;"
        @click="
          orderByClick(
            'negative_num',
            orderBy == 'negative_num'
              ? orderByType == 'asc'
                ? 'desc'
                : 'asc'
              : 'asc'
          )
        "
      >
        <span>涉济负面占比</span>
        <svg-icon
          :size="20"
          v-show="orderBy == 'negative_num' && orderByType == 'asc'"
          icon-class="哨点-升序"
          style="width: 6.87px; height: 11.95px; margin-left: 8px;"
        />
        <svg-icon
          :size="20"
          v-show="orderBy == 'negative_num' && orderByType == 'desc'"
          icon-class="哨点-降序"
          style="width: 6.87px; height: 11.95px; margin-left: 8px;"
        />
      </div>
      <div
        class="item"
        style="width: 100px;"
        @click="
          orderByClick(
            'deal_num',
            orderBy == 'deal_num'
              ? orderByType == 'asc'
                ? 'desc'
                : 'asc'
              : 'asc'
          )
        "
      >
        <span>处置次数</span>
        <svg-icon
          :size="20"
          v-show="orderBy == 'deal_num' && orderByType == 'asc'"
          icon-class="哨点-升序"
          style="width: 6.87px; height: 11.95px; margin-left: 8px;"
        />
        <svg-icon
          :size="20"
          v-show="orderBy == 'deal_num' && orderByType == 'desc'"
          icon-class="哨点-降序"
          style="width: 6.87px; height: 11.95px; margin-left: 8px;"
        />
      </div>
      <div class="item" style="width: 140px;">账号等级</div>
      <div
        class="item"
        style="width: 120px;"
        @click="
          orderByClick(
            'update_time',
            orderBy == 'update_time'
              ? orderByType == 'asc'
                ? 'desc'
                : 'asc'
              : 'asc'
          )
        "
      >
        <span>更新日期</span>
        <svg-icon
          :size="20"
          v-show="orderBy == 'update_time' && orderByType == 'asc'"
          icon-class="哨点-升序"
          style="width: 6.87px; height: 11.95px; margin-left: 8px;"
        />
        <svg-icon
          :size="20"
          v-show="orderBy == 'update_time' && orderByType == 'desc'"
          icon-class="哨点-降序"
          style="width: 6.87px; height: 11.95px; margin-left: 8px;"
        />
      </div>
      <div class="item" style="width: 80px;">操作</div>
    </div>
    <div class="content">
      <Spin v-show="loading" fix>
        <Icon type="ios-loading" size="18" class="demo-spin-icon-load"></Icon>
        <div>Loading</div>
      </Spin>
      <no-data v-show="list.length === 0 && !loading" />

      <div class="row flex" v-for="(item, index) in list" :key="index + 1">
        <div class="item" style="width: 70px;">
          <Checkbox :label="item.id"
            >{{ (pageNo - 1) * pageSize + index + 1 }}
          </Checkbox>
        </div>
        <div
          class="item  cp"
          style="width: 175px; text-align: left;"
          @click="goDetail(item)"
        >
          <platformIcon :id="item.situation"></platformIcon>
          <span class="ellipsis" :title="item.accountName"> {{ item.accountName }}</span>
          <svg-icon icon-class="电话" v-if="item.phone==1" style="width: 16px;height: 16px;" />
          
        </div>
        <div class="item" style="width: 140px;">
          {{ item.mediumName }}
        </div>
        <div class="item" style="width: 100px;">
          {{ item.userId }}
        </div>
        <div class="item" style="width: 140px;">
          {{ item.fansCount }}
        </div>
        <div class="item" style="width: 140px;">
          {{ item.pushNum == null ? '-' : item.pushNum }}
        </div>
        <div class="item" style="width: 140px;">
          {{
            item.pushNum == null || item.pushNum === ''
              ? '-'
              : item.pushNum
                ? (item.negativeNum / item.pushNum === 0 ||
                   item.negativeNum / item.pushNum === 1
                    ? (item.negativeNum / item.pushNum * 100).toFixed(0) + '%'
                    : (item.negativeNum / item.pushNum * 100).toFixed(2) + '%'
                  )
                : '0%'
          }}
        </div>
        <div class="item" style="width: 100px;">
          {{ item.dealNum }}
        </div>
        <div class="item" style="width: 140px;">
          <!-- {{ item.level }} -->
          <Select
            :value="item.level"
            style="width: 100px;"
            @on-change="
              (value) => {
                changeLevel(item, value);
              }
            "
          >
            <Option
              v-for="item in levelList"
              :value="item.value"
              :key="item.value"
              >{{ item.label }}</Option
            >
          </Select>
        </div>
        <div class="item" style="width: 140px;">
          {{
            item.updateTime ? moment(item.updateTime).format("YYYY-MM-DD") : ""
          }}
        </div>
        <div
          class="item"
          style="width: 80px; display: flex; justify-content: center;"
          @click.stop="() => {}"
        >
          <span title="账号转移" style="margin-right: 10px;" @click="transferAccount(item)">
            <svg-icon icon-class="账号转移" style="color: #2d8cf0;" />
          </span>
          <Poptip
            transfer
            confirm
            title="
                  是否删除该账号！
                "
            @on-ok="del(item.id)"
          >
            <span title="删除">
              <svg-icon icon-class="列表-删除" />
            </span>
          </Poptip>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import moment from "moment";
import platformIcon from "@/components/platformIcon";
// 这里可以导入其他文件（比如：组件，工具 js，第三方插件 js，json 文件，图片文件等等）
// 例如：import  《组件名称》  from '《组件路径》 ';

export default {
  data() {
    // 这里存放数据
    return {
      levelList: [
        {
          value: -1,
          label: "请选择",
        },
        {
          value: 1,
          label: "一级",
        },
        {
          value: 2,
          label: "二级",
        },
        {
          value: 3,
          label: "三级",
        },
      ],
    };
  },
  // import 引入的组件需要注入到对象中才能使用
  components: {
    platformIcon,
  },
  props: {
    list: {
      default: [],
    },
    pageSize: {
      default: 0,
    },
    pageNo: {
      default: 0,
    },
    loading: {},
    orderBy: {},
    orderByType: {},
  },
  // 生命周期 - 创建完成（可以访问当前this 实例）
  created() {},
  // 方法集合
  methods: {
    del(id) {
      let params = {};
      params.ids = id;
      this.$http
        .post("/accountInfoNew/deleteAccount", {}, { params })
        .then((res) => {
          if (res.body.status === 0) {
            this.$Message.success(res.body.message);
            this.$emit("load");
          }
        });
    },

    orderByClick(key, type) {
      this.$emit("orderByChange", key, type);
    },
    goDetail(item) {
      const { href } = this.$router.resolve({
        path: "/main/archiveDetail",
        query: {
          // id: item.id,
          ukey: item.userId,
          situation: item.situation,

        },
      });
      window.open(href, "_blank");
    },
    changeLevel(item, value) {
      console.log(item, value);
      if (!value && value !== 0 && value !== false) {
          console.warn('无效的 value 值', value);
          return;
      }
      this.$http
        .post(
          "/accountInfoNew/editLevel",
          {},
          {
            params: {
              ids: item.id,
              level: value,
            },
          }
        )
        .then((res) => {
          if (res.body.status === 0) {
            item.level = value;
            this.$Message.success(res.body.message);
          } else {
            this.$Message.error(res.body.message);
          }
        });
    },

    transferAccount(item) {
      // 打开账号转移弹窗
      const TransferAccount = () => import("@/views/main/Archive/selfMediaLibrary/components/transferAccount.vue");
      this.$modal.show({
        component: TransferAccount,
        componentProps: {
          accountInfo: item,
        },
        componentEvents: {
          close: (result) => {
            if (result === 'success') {
              this.$emit("load");
            }
            this.$modal.hide();
          },
        },
        title: "账号转移",
        width: 900,
      });
    },
  },
  // 计算属性 类似于 data 概念
  computed: {
    moment() {
      return moment;
    },
  },
  // 监控 data 中的数据变化
  watch: {},
  //过滤器
  filters: {},
  // 生命周期 - 挂载完成（可以访问 DOM 元素）
  mounted() {},
};
</script>
<style scoped lang="less">
.modal-container{
  max-width: 1200px;
  width: 1200px;
}
.listBox {
  .header {
    line-height: 70px;
    height: 70px;
    font-weight: 600;
    color: #333333;
    font-size: 16px;
    padding-right: 6px;
    .item {
      display: flex;
      align-items: center;
      justify-content: center;
      span,
      /deep/ svg {
        cursor: pointer;
      }
    }
  }
}
.item {
  text-align: center;
  flex-shrink: 0;
  &.cp{
    display: flex;
    align-items: center;

    svg{
      min-width:20px!important;
    }
    span{
      
    }
  }
}
.content {
  height: calc(~"100vh - 310px");
  overflow-y: auto;
  position: relative;
  overflow-x: hidden;
  .row {
    height: 64px;
    align-items: center;

    &:nth-child(2n-1) {
      background-color: #f6f7fc;
    }

    &:nth-child(2n) {
      background-color: #fafafa;
    }

    .svg-icon {
      height: 20px;
      width: 20px;
      margin-right: 5px;
      cursor: pointer;

      &:nth-child(1) {
        height: 22px;
        width: 22px;
        margin-right: 5px;
      }
    }
  }
}
</style>
