<template>
  <div class="archive-overview">
    <!-- 统计时段选择器 -->
    <div class="time-period-selector">
      <StatTimePeriod @change="handleTimePeriodChange" />
    </div>

    <!-- 自媒体库概览 -->
    <div class="overview-section">
      <h3 class="section-title">自媒体库概览</h3>
      <div class="overview-grid">
        <div class="pbox">
            <!-- 数据量统计 -->
            <div class="overview-item" style="min-width: 438px;">
              <DataStatistics :timeRange="currentTimeRange" />
            </div>
            
            <!-- 账号来源统计 -->
            <div class="overview-item">
              <AccountSourceStats :timeRange="currentTimeRange" />
            </div>
        </div>
        <!-- 个人信息完善排行 -->
        <div class="overview-item">
          <PersonalInfoRanking :timeRange="currentTimeRange" />
        </div>
      </div>
      
      <div class="overview-grid">
        <!-- 涉济活跃账号 -->
        <div class="overview-item">
          <ActiveAccountStats :timeRange="currentTimeRange" />
        </div>
        
        <!-- 涉济处置账号 -->
        <div class="overview-item">
          <DisposalAccountStats :timeRange="currentTimeRange" />
        </div>
      </div>

      <div class="overview-grid"> 
          <!-- 媒体库概览 -->
          <div class="overview-item noborder">
            <h3 class="section-title">媒体库概览</h3>
            <MediaLibraryStats :timeRange="currentTimeRange" />
          </div>
          <!-- 属地网站概览 -->
           <div class="overview-item noborder">
            <h3 class="section-title">属地网站概览</h3>
          <LocalWebsiteStats :timeRange="currentTimeRange" />
        </div>
      </div>
    </div>

  </div>
</template>

<script>
// 导入各个统计组件
import StatTimePeriod from './components/StatTimePeriod.vue'              // 统计时段选择器
import DataStatistics from './components/DataStatistics.vue'              // 数据量统计
import AccountSourceStats from './components/AccountSourceStats.vue'      // 账号来源统计
import PersonalInfoRanking from './components/PersonalInfoRanking.vue'    // 个人信息完善排行
import ActiveAccountStats from './components/ActiveAccountStats.vue'      // 涉济活跃账号
import DisposalAccountStats from './components/DisposalAccountStats.vue'  // 涉济处置账号
import MediaLibraryStats from './components/MediaLibraryStats.vue'        // 自媒体库概览
import LocalWebsiteStats from './components/LocalWebsiteStats.vue' // 属地网站统计

// 导入时间参数工具
import { buildTimeParams } from './utils/timeParamsBuilder.js'

export default {
  name: 'ArchiveOverview',
  components: {
    StatTimePeriod,
    DataStatistics,
    AccountSourceStats,
    PersonalInfoRanking,
    ActiveAccountStats,
    DisposalAccountStats,
    MediaLibraryStats,
    LocalWebsiteStats
  },
  data() {
    return {
      // 当前选择的时间范围
      currentTimeRange: {
        type: 'all', // all, day, week, month, custom
        startDate: null,
        endDate: null,
        label: '全部'
      }
    }
  },
  created() {
    // 记录页面访问日志
    this.getLog(this.$route.meta.moduleName);
  },
  methods: {
    // 处理时间段变化
    handleTimePeriodChange(timeRange) {
      this.currentTimeRange = timeRange;
      console.log('时间范围变化:', timeRange);
    }
  }
}
</script>

<style lang="less" scoped>
.pbox{
  display: flex;
  gap: 20px;
  flex-direction: row;
  .overview-item{
    flex: 1;
  }
}
.archive-overview {
   margin:20px 20px 20px 0;
  min-height: 100vh;
  border-radius:8px ;
  overflow: hidden;
  border-radius: 8px;
  overflow: hidden;

  .time-period-selector {
    // margin-bottom: 20px;
  }

  .overview-section {
    background: white;
    
    padding: 20px;
    .section-title {
      font-weight: 500;
      font-size: 20px;
      color: #5D5D5D;
      line-height: 28px;
      display: flex;
      flex-direction: row;
      align-items: center;
      margin-bottom:20px;
      &::before{
        content:"";
        width: 6px;
        height: 15px;
        background: #5585EC;
        margin-right: 5px;
      }
      
    }

    .overview-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
      gap: 20px;
      margin-bottom: 20px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .overview-item {
      border-radius: 6px;
      padding: 15px;
      background: #FFFFFF;
      box-shadow: 0px 3px 6px 0px rgba(0,0,0,0.16);
      &.noborder{
        box-shadow: 0px 0px 0px 0px rgba(0,0,0,0.16);
      }
      &.full-width {
        grid-column: 1 / -1;
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .archive-overview .overview-section .overview-grid {
    grid-template-columns: 1fr;
  }
}
</style>
