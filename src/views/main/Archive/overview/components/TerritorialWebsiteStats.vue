<template>
  <div class="territorial-website-stats">
    <h4 class="component-title">属地网站概览</h4>
    
    <div class="stats-content">
      <!-- 柱状图 -->
      <div class="chart-container">
        <div ref="barChart" class="bar-chart"></div>
      </div>
      
      <!-- 总量显示 -->
      <div class="total-display">
        <div class="total-card">
          <svg-icon icon-class="网站总量" style="font-size: 32px; color: #2d8cf0;" />
          <div class="total-content">
            <div class="total-number">{{ formatNumber(totalWebsites) }}</div>
            <div class="total-label">属地网站总量</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <Spin size="large" />
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'TerritorialWebsiteStats',
  props: {
    timeRange: {
      type: Object,
      default: () => ({
        type: 'all',
        startDate: null,
        endDate: null,
        label: '全部'
      })
    }
  },
  data() {
    return {
      loading: false,
      chart: null,
      chartData: []
    }
  },
  computed: {
    totalWebsites() {
      return this.chartData.reduce((sum, item) => sum + item.value, 0)
    }
  },
  watch: {
    timeRange: {
      handler() {
        // this.fetchData() // 暂时注释掉接口调用
      },
      deep: true
    }
  },
  methods: {
    // 获取数据
    async fetchData() {
      // 暂时注释掉接口调用，使用模拟数据
      /*
      this.loading = true
      try {
        const params = this.buildTimeParams()

        // 调用接口获取属地网站统计
        const response = await this.$http.get('/archive/territorialWebsiteStats', { params })

        if (response.body.status === 0) {
          this.chartData = response.body.data || this.getMockData()
          this.updateChart()
        }
      } catch (error) {
        console.error('获取属地网站统计失败:', error)
        // 使用模拟数据
        this.chartData = this.getMockData()
        this.updateChart()
      } finally {
        this.loading = false
      }
      */
      // 使用模拟数据
      this.chartData = this.getMockData()
      this.updateChart()
    },

    // 获取模拟数据（按照系统上方显示顺序）
    getMockData() {
      return [
        { name: '历下区', value: 14422 },
        { name: '市中区', value: 5101 },
        { name: '槐荫区', value: 5422 },
        { name: '天桥区', value: 6494 },
        { name: '历城区', value: 11043 },
        { name: '长清区', value: 1586 },
        { name: '章丘区', value: 2295 },
        { name: '济阳区', value: 1639 },
        { name: '莱芜区', value: 1275 },
        { name: '钢城区', value: 261 },
        { name: '平阴县', value: 383 },
        { name: '商河县', value: 415 },
        { name: '高新区', value: 2086 },
        { name: '南部山区', value: 2 },
        { name: '起步区', value: 18 },
        { name: '其他', value: 1362 }
      ]
    },

    // 构建时间参数
    buildTimeParams() {
      const params = {}
      
      if (this.timeRange.type !== 'all') {
        if (this.timeRange.startDate) {
          params.startDate = this.formatDate(this.timeRange.startDate)
        }
        if (this.timeRange.endDate) {
          params.endDate = this.formatDate(this.timeRange.endDate)
        }
      }
      
      return params
    },

    // 格式化日期
    formatDate(date) {
      const d = new Date(date)
      return d.toISOString().split('T')[0]
    },

    // 格式化数字
    formatNumber(num) {
      if (num >= 10000) {
        return (num / 10000).toFixed(1) + '万'
      }
      return num.toLocaleString()
    },

    // 初始化图表
    initChart() {
      this.chart = echarts.init(this.$refs.barChart)
      this.updateChart()
    },

    // 更新图表
    updateChart() {
      if (!this.chart || !this.chartData.length) return

      const categories = this.chartData.map(item => item.name)
      const values = this.chartData.map(item => item.value)
      const maxValue = Math.max(...values)

      const option = {
        grid: {
          top: 30,
          right: 30,
          bottom: 60,
          left: 60
        },
        xAxis: {
          type: 'category',
          data: categories,
          axisLabel: {
            rotate: 45,
            fontSize: 12,
            color: '#666',
            interval: 0
          },
          axisLine: {
            lineStyle: {
              color: '#e8e8e8'
            }
          }
        },
        yAxis: {
          type: 'value',
          max: Math.ceil(maxValue * 1.1),
          axisLabel: {
            fontSize: 12,
            color: '#666',
            formatter: function(value) {
              if (value >= 10000) {
                return (value / 10000).toFixed(1) + '万'
              }
              return value
            }
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          splitLine: {
            lineStyle: {
              color: '#f0f0f0',
              type: 'dashed'
            }
          }
        },
        series: [{
          type: 'bar',
          data: values.map((value) => ({
            value,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#4facfe' },
                { offset: 1, color: '#00f2fe' }
              ])
            }
          })),
          barWidth: '60%',
          label: {
            show: true,
            position: 'top',
            fontSize: 10,
            color: '#333',
            formatter: function(params) {
              if (params.value >= 10000) {
                return (params.value / 10000).toFixed(1) + '万'
              }
              return params.value
            }
          }
        }],
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function(params) {
            const data = params[0]
            return `${data.name}<br/>网站数量: ${data.value.toLocaleString()}`
          }
        }
      }

      this.chart.setOption(option)
    },

    // 处理窗口大小变化
    handleResize() {
      if (this.chart) {
        this.chart.resize()
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
      this.fetchData() // 使用模拟数据
    })

    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose()
    }
    window.removeEventListener('resize', this.handleResize)
  }
}
</script>

<style lang="less" scoped>
.territorial-website-stats {
  position: relative;

  .component-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e8e8e8;
  }

  .stats-content {
    display: flex;
    gap: 20px;
    align-items: flex-start;

    .chart-container {
      flex: 1;
      height: 400px;

      .bar-chart {
        width: 100%;
        height: 100%;
      }
    }

    .total-display {
      width: 200px;
      
      .total-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 12px;
        padding: 30px 20px;
        text-align: center;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);

        .total-content {
          margin-top: 15px;

          .total-number {
            font-size: 32px;
            font-weight: bold;
            line-height: 1;
            margin-bottom: 8px;
          }

          .total-label {
            font-size: 14px;
            opacity: 0.9;
          }
        }
      }
    }
  }

  .loading-container {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 6px;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .territorial-website-stats .stats-content {
    flex-direction: column;
    
    .total-display {
      width: 100%;
      
      .total-card {
        display: flex;
        align-items: center;
        gap: 20px;
        text-align: left;
        padding: 20px;
        
        .total-content {
          margin-top: 0;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .territorial-website-stats .stats-content .chart-container {
    height: 300px;
  }
}
</style>
