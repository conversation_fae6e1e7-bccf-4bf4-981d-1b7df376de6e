<template>
  <div class="account-source-stats">
    <h4 class="component-title">账号来源统计</h4>
    
    <div class="chart-container">
      <!-- 图表区域 -->
      <div class="chart-wrapper">
        <div ref="pieChart" class="pie-chart"></div>

        <!-- 中心显示 -->
        <div class="chart-center">
          <div class="center-percentage">{{ centerPercentage }}%</div>
          <div class="center-label">{{ centerLabel }}</div>
        </div>
      </div>

      <!-- 图例区域 -->
      <div class="legend-area">
        <div
          v-for="(item, index) in chartData"
          :key="index"
          class="legend-item"
          @mouseenter="highlightSector(index)"
          @mouseleave="resetHighlight"
        >
          <div
            class="legend-color"
            :style="{ backgroundColor: item.color }"
          ></div>
          <span class="legend-label">{{ item.name }}</span>
          
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <Spin size="large" />
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { buildTimeParams } from '../utils/timeParamsBuilder.js'

export default {
  name: 'AccountSourceStats',
  props: {
    timeRange: {
      type: Object,
      default: () => ({
        type: 'all',
        startDate: null,
        endDate: null,
        label: '全部'
      })
    }
  },
  data() {
    return {
      loading: false,
      chart: null,
      chartData: [
        { name: '三方提供', value: 0, color: '#F65177' },
        { name: '用户添加', value: 0, color: '#5585EC' },
        { name: '系统更新', value: 0, color: '#47DDC0' }
      ],
      centerPercentage: 52,
      centerLabel: '系统更新'
    }
  },
  watch: {
    timeRange: {
      handler() {
        this.fetchData() // 启用接口调用
      },
      deep: true
    }
  },
  methods: {
    // 获取数据
    async fetchData() {
      this.loading = true
      try {
        const params = buildTimeParams(this.timeRange)

        // 调用接口获取账号来源统计
        const response = await this.$http.get('/holoArchiveChart/getAccountSourceCount', { params })

        if (response.body.status === 0) {
          const data = response.body.data
          this.processChartData(data)
          this.updateChart()
          this.updateCenterDisplay()
        }
      } catch (error) {
        console.error('获取账号来源统计失败:', error)
        this.$Message.error('获取账号来源统计失败')
      } finally {
        this.loading = false
      }
    },

    // 处理图表数据
    processChartData(data) {
      // 数据来源名字对应关系
      const sourceMapping = {
        '0': '系统更新',
        '1': '三方提供',
        '2': '用户添加'
      }

      // 重置图表数据
      this.chartData = [
        { name: '三方提供', value: 0, color: '#F65177' },
        { name: '用户添加', value: 0, color: '#5585EC' },
        { name: '系统更新', value: 0, color: '#47DDC0' }
      ]

      // 处理接口返回的数据
      if (data && Array.isArray(data)) {
        data.forEach(item => {
          const sourceName = sourceMapping[item.groupBy]
          if (sourceName) {
            const chartItem = this.chartData.find(chart => chart.name === sourceName)
            if (chartItem) {
              chartItem.value = item.statNum || 0
            }
          }
        })
      }
    },



    // 初始化图表
    initChart() {
      this.chart = echarts.init(this.$refs.pieChart)
      this.updateChart()
      
      // 监听图表事件
      this.chart.on('mouseover', (params) => {
        this.centerPercentage = this.calculatePercentage(params.value)
        this.centerLabel = params.name
      })
      
      this.chart.on('mouseout', () => {
        this.updateCenterDisplay()
      })
    },

    // 更新图表
    updateChart() {
      if (!this.chart) return

      const option = {
        series: [{
          type: 'pie',
          radius: ['60%', '80%'],
          center: ['50%', '50%'],
          avoidLabelOverlap: false,
          label: {
            show: false
          },
          labelLine: {
            show: false
          },
          data: this.chartData.map(item => ({
            value: item.value,
            name: item.name,
            itemStyle: {
              color: item.color
            }
          }))
        }]
      }

      this.chart.setOption(option)
    },

    // 更新中心显示
    updateCenterDisplay() {
      const total = this.chartData.reduce((sum, item) => sum + item.value, 0)
      if (total === 0) {
        this.centerPercentage = 0
        this.centerLabel = '暂无数据'
        return
      }

      // 找到最大值的项目
      const maxItem = this.chartData.reduce((max, item) => 
        item.value > max.value ? item : max
      )
      
      this.centerPercentage = this.calculatePercentage(maxItem.value)
      this.centerLabel = maxItem.name
    },

    // 计算百分比
    calculatePercentage(value) {
      const total = this.chartData.reduce((sum, item) => sum + item.value, 0)
      return total === 0 ? '0.00' : ((value / total) * 100).toFixed(2)
    },

    // 高亮扇形
    highlightSector(index) {
      if (this.chart) {
        this.chart.dispatchAction({
          type: 'highlight',
          seriesIndex: 0,
          dataIndex: index
        })
        
        const item = this.chartData[index]
        this.centerPercentage = this.calculatePercentage(item.value)
        this.centerLabel = item.name
      }
    },

    // 重置高亮
    resetHighlight() {
      if (this.chart) {
        this.chart.dispatchAction({
          type: 'downplay',
          seriesIndex: 0
        })
        
        this.updateCenterDisplay()
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
      this.fetchData() // 启用接口调用
    })
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose()
    }
  }
}
</script>

<style lang="less" scoped>
.account-source-stats {
  position: relative;

  .component-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
    padding-bottom: 8px;
    width:222px;
    height: 36px;
    line-height: 36px;
    position: relative;
    margin:-15px auto 24px auto;
    text-align: center;
    background-image: url("../img/矩形.svg");
  }

  .chart-container {
    display: flex;
    align-items: center;


    .chart-wrapper {
      position: relative;
      width: 260px;
      height: 260px;

      .pie-chart {
        width: 100%;
        height: 100%;
      }

      .chart-center {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        text-align: center;
        pointer-events: none;

        .center-percentage {
          font-size: 24px;
          font-weight: bold;
          color: #333;
          line-height: 1;
        }

        .center-label {
          font-size: 12px;
          color: #666;
          margin-top: 4px;
        }
      }
    }

    .legend-area {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 12px;

      .legend-item {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px 0px;
        border-radius: 4px;
        cursor: pointer;
        transition: background-color 0.2s;

        &:hover {
          background-color: #f5f5f5;
        }

        .legend-color {
          width: 12px;
          height: 12px;
          border-radius: 2px;
        }

        .legend-label {
          flex: 1;
          font-size: 14px;
          color: #333;
        }

        .legend-value {
          font-size: 14px;
          font-weight: 500;
          color: #666;
        }
      }
    }
  }

  .loading-container {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 6px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .account-source-stats .chart-container {
    flex-direction: column;
    
    .chart-wrapper {
      width: 150px;
      height: 150px;
    }
  }
}
</style>
