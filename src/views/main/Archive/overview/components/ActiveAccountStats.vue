<template>
  <div class="active-account-stats">
    <div class="component-title">涉济活跃账号</div>

    <div class="chart-container" v-loading="loading">
      <div v-show="!hasData && !loading" class="empty-container">
        <EmptyState text="暂无数据" description="当前时间范围内没有涉济活跃账号数据" height="350px" />
      </div>
      <div v-show="hasData && !loading">
        <!-- 图例 -->
        <div class="legend">
          <div class="legend-item">
            <span class="legend-color" style="background:#E8E8E8 ;"></span>
            <span class="legend-text">涉济发文量</span>
          </div>
          <div class="legend-item">
            <span class="legend-color gradient-color"></span>
            <span class="legend-text">负面信息量</span>
          </div>
        </div>

        <!-- 图表 -->
        <div ref="barChart" class="bar-chart"></div>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import EmptyState from './EmptyState.vue'
import { buildTimeParams } from '../utils/timeParamsBuilder.js'

export default {
  name: 'ActiveAccountStats',
  components: {
    EmptyState
  },
  props: {
    timeRange: {
      type: Object,
      default: () => ({
        type: 'all',
        startDate: null,
        endDate: null,
        label: '全部'
      })
    }
  },
  data() {
    return {
      loading: false,
      chart: null,
      chartData: []
    }
  },
  computed: {
    hasData() {
      return this.chartData && this.chartData.length > 0
    }
  },
  watch: {
    timeRange: {
      handler() {
        this.fetchData() // 暂时注释掉接口调用
      },
      deep: true
    }
  },
  methods: {
    // 获取数据
    async fetchData() {
      this.loading = true
      try {
        const params = buildTimeParams(this.timeRange)
        // 添加排序参数，按涉济发文量排序
        params.orderBy = 'push_num'
        params.limit = '10' // 显示前10个

        // 调用接口获取涉济活跃账号统计
        const response = await this.$http.get('/holoArchiveChart/infoList', { params })

        if (response.body.status === 0) {
          this.chartData = this.processApiData(response.body.data) || []
        } else {
          this.chartData = []
        }
      } catch (error) {
        // console.error('获取涉济活跃账号统计失败:', error)
        // // 使用模拟数据
        // this.chartData = this.getMockData()
        //展示暂无数据
        this.chartData = []
      } finally {
        this.loading = false
        // 确保在数据加载完成后更新图表
        this.$nextTick(() => {
          this.updateChart()
        })
      }
    },

    // 处理接口返回的数据
    processApiData(data) {
      if (!data || !Array.isArray(data)) return []

      return data.map(item => ({
        name: item.accountName || item.name || '未知账号',
        platform: this.getPlatformKey(item.situation),
        platformIcon: this.getPlatformIcon(item.situation),
        articleCount: item.pushNum || 0,
        negativeCount: item.negativeNum || 0,
        situation: item.situation,
        userId: item.userId || item.ukey || null
      }))
    },

    // 根据situation获取平台key
    getPlatformKey(situation) {
      const platformMap = {
        10: 'weibo',
        20: 'wechat',
        30: 'news',
        31: 'app',
        48: 'douyin',
        50: 'kuaishou',
        60: 'forum',
        80: 'xiaohongshu',
        600: 'toutiao'
      }
      return platformMap[situation] || 'other'
    },

    // 根据situation获取平台图标
    getPlatformIcon(situation) {
      const iconMap = {
       
        41: "论坛跟帖",
        51: "贴吧跟帖",
        80: "小红书",
        48: "视频",
        50: "视频",
        110: "火山",
        188: "视频",
        199: "视频",
        30: "新闻",
        31: "新闻app",
        10: "微博",
        20: "微信公号",
        80: "小红书",
        60: "论坛",
        61: "论坛",
        62: "贴吧",
        170: "知乎",
        230: "自媒体",
        300: "视频300",
        200: "视频",
        320: "新闻网站",
        400: "境外新闻网站",
        501: "Facebook",
        502: "推特",
        600: "今日头条",
      }
      return iconMap[situation] || '新媒体'
    },



    // 获取模拟数据 - 前10个账号的涉济发文量和负面信息量
    getMockData() {
      return [
        {
          name: '爱济南',
          platform: 'weibo',
          platformIcon: '微博',
          articleCount: 85,
          negativeCount: 15,
          situation: 10,
          userId: 'addJcHand_113449'
        },
        {
          name: '济南日报',
          platform: 'toutiao',
          platformIcon: '头条',
          articleCount: 80,
          negativeCount: 12,
          situation: 600,
          userId: 'addJcHand_113450'
        },
        {
          name: '济南新闻',
          platform: 'wechat',
          platformIcon: '微信',
          articleCount: 65,
          negativeCount: 8,
          situation: 20,
          userId: 'addJcHand_113451'
        },
        {
          name: '济南12345',
          platform: 'xiaohongshu',
          platformIcon: '小红书-copy-copy',
          articleCount: 60,
          negativeCount: 10,
          situation: 80,
          userId: 'addJcHand_113452'
        },
        {
          name: '山东泉城',
          platform: 'douyin',
          platformIcon: '抖音',
          articleCount: 35,
          negativeCount: 25,
          situation: 48,
          userId: 'addJcHand_113453'
        },
        {
          name: '齐鲁新闻',
          platform: 'weibo',
          platformIcon: '微博',
          articleCount: 75,
          negativeCount: 5,
          situation: 10,
          userId: 'addJcHand_113454'
        },
        {
          name: '山东',
          platform: 'toutiao',
          platformIcon: '头条',
          articleCount: 45,
          negativeCount: 15,
          situation: 600,
          userId: 'addJcHand_113455'
        },
        {
          name: '山东晚报',
          platform: 'wechat',
          platformIcon: '微信',
          articleCount: 25,
          negativeCount: 20,
          situation: 20,
          userId: 'addJcHand_113456'
        },
        {
          name: '人民济南',
          platform: 'weibo',
          platformIcon: '微博',
          articleCount: 30,
          negativeCount: 8,
          situation: 10,
          userId: 'addJcHand_113457'
        },
        {
          name: '山东新闻',
          platform: 'douyin',
          platformIcon: '抖音',
          articleCount: 15,
          negativeCount: 10,
          situation: 48,
          userId: 'addJcHand_113458'
        }
      ]
    },



    // 初始化图表
    initChart() {
      if (!this.$refs.barChart) return
      this.chart = echarts.init(this.$refs.barChart)
      this.updateChart()

      // 添加点击事件监听
      this.chart.on('click', (params) => {
        this.handleBarClick(params)
      })
    },

    // 更新图表
    updateChart() {
      // 如果没有数据，清空图表
      if (!this.hasData) {
        if (this.chart) {
          this.chart.clear()
        }
        return
      }

      // 确保图表已初始化
      if (!this.chart && this.$refs.barChart) {
        this.chart = echarts.init(this.$refs.barChart)
      }

      if (!this.chart) return

      // 按发文量排序
      const sortedData = [...this.chartData].sort((a, b) => a.articleCount - b.articleCount)

      const categories = sortedData.map(item => item.name)
      const articleData = sortedData.map(item => item.articleCount)
      const negativeData = sortedData.map(item => item.negativeCount)

      const option = {
        grid: {
          top: 20,
          right: 30,
          bottom: 40,
          left: 80
        },
        xAxis: {
          type: 'value',
          axisLabel: {
            fontSize: 10,
            color: '#666'
          },
          splitLine: {
            lineStyle: {
              color: '#f0f0f0',
              type: 'dashed'
            }
          },
          axisLine: {
            lineStyle: {
              color: '#e8e8e8'
            }
          }
          
        },
        yAxis: {
          type: 'category',
          data: categories,
          axisLabel: {
            fontSize: 10,
            color: '#666'
          },
          axisLine: {
            lineStyle: {
              color: '#e8e8e8'
            }
          },
          axisTick: {
            show: false
          }
        },
        series: [
          {
            name: '涉济发文量',
            type: 'bar',
            data: articleData,
            itemStyle: {
              color: '#E8E8E8'
            },
            barWidth: 16,
            barGap: '-100%', // 关键：让柱状图完全重叠
            z: 1
          },
          {
            name: '负面信息量',
            type: 'bar',
            data: negativeData,
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 1,
                y2: 0,
                colorStops: [
                  { offset: 0, color: '#26A69A' },
                  { offset: 1, color: '#4ECDC4' }
                ]
              },

            },
            barWidth: 16,
            z: 2
          }
        ],
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: (params) => {
            // 找到对应的原始数据
            const accountName = params[0].name
            const accountData = sortedData.find(item => item.name === accountName)

            if (accountData) {
              // 获取平台图标的SVG
              const iconName = accountData.platformIcon || '新媒体'
              const svgIcon = this.getSvgIcon(iconName)

              let result = `<div style="display: flex; align-items: center; margin-bottom: 8px;">
                ${svgIcon}
                <span style="margin-left: 6px; font-weight: bold;">${accountName}</span>
              </div>`
              result += `<div style="margin-left: 20px;">涉济发文量: ${accountData.articleCount}</div>`
              result += `<div style="margin-left: 20px;">负面信息量: ${accountData.negativeCount}</div>`
              return result
            } else {
              let result = `${params[0].name}<br/>`
              params.forEach(param => {
                result += `${param.seriesName}: ${param.value}<br/>`
              })
              return result
            }
          }
        }
      }

      this.chart.setOption(option)

      // 重新添加点击事件监听器（确保在setOption后添加）
      this.chart.off('click') // 先移除之前的监听器
      this.chart.on('click', (params) => {
        this.handleBarClick(params)
      })
    },

    // 获取SVG图标HTML
    getSvgIcon(iconName) {
      // 根据图标名称返回对应的SVG HTML
      const iconMap = {
        '微博': `<svg width="16" height="16" viewBox="0 0 16 16" fill="#FF6B35"><use xlink:href="#icon-微博"></use></svg>`,
        '微信公众号': `<svg width="16" height="16" viewBox="0 0 16 16" fill="#07C160"><use xlink:href="#icon-微信"></use></svg>`,
        '微信公号': `<svg width="16" height="16" viewBox="0 0 16 16" fill="#07C160"><use xlink:href="#icon-微信"></use></svg>`,
        '微信': `<svg width="16" height="16" viewBox="0 0 16 16" fill="#07C160"><use xlink:href="#icon-微信"></use></svg>`,
        '新闻网站': `<svg width="16" height="16" viewBox="0 0 16 16" fill="#4285F4"><use xlink:href="#icon-新闻"></use></svg>`,
        '新闻APP': `<svg width="16" height="16" viewBox="0 0 16 16" fill="#4285F4"><use xlink:href="#icon-新闻APP"></use></svg>`,
        '新闻app': `<svg width="16" height="16" viewBox="0 0 16 16" fill="#4285F4"><use xlink:href="#icon-新闻APP"></use></svg>`,
        '新闻': `<svg width="16" height="16" viewBox="0 0 16 16" fill="#4285F4"><use xlink:href="#icon-新闻"></use></svg>`,
        '小红书': `<svg width="16" height="16" viewBox="0 0 16 16" fill="#FF2442"><use xlink:href="#icon-小红书"></use></svg>`,
        '抖音': `<svg width="16" height="16" viewBox="0 0 16 16" fill="#000000"><use xlink:href="#icon-抖音"></use></svg>`,
        '快手': `<svg width="16" height="16" viewBox="0 0 16 16" fill="#FF6600"><use xlink:href="#icon-快手"></use></svg>`,
        '今日头条': `<svg width="16" height="16" viewBox="0 0 16 16" fill="#FF4500"><use xlink:href="#icon-今日头条"></use></svg>`,
        '头条': `<svg width="16" height="16" viewBox="0 0 16 16" fill="#FF4500"><use xlink:href="#icon-今日头条"></use></svg>`,
        '论坛': `<svg width="16" height="16" viewBox="0 0 16 16" fill="#8B5CF6"><use xlink:href="#icon-论坛"></use></svg>`,
        '贴吧': `<svg width="16" height="16" viewBox="0 0 16 16" fill="#3B82F6"><use xlink:href="#icon-贴吧"></use></svg>`,
        '知乎': `<svg width="16" height="16" viewBox="0 0 16 16" fill="#0084FF"><use xlink:href="#icon-知乎"></use></svg>`,
        '视频': `<svg width="16" height="16" viewBox="0 0 16 16" fill="#EF4444"><use xlink:href="#icon-视频"></use></svg>`,
        '火山': `<svg width="16" height="16" viewBox="0 0 16 16" fill="#FF4500"><use xlink:href="#icon-火山"></use></svg>`,
        '自媒体': `<svg width="16" height="16" viewBox="0 0 16 16" fill="#6B7280"><use xlink:href="#icon-自媒体"></use></svg>`,
        'Facebook': `<svg width="16" height="16" viewBox="0 0 16 16" fill="#1877F2"><use xlink:href="#icon-Facebook"></use></svg>`,
        '推特': `<svg width="16" height="16" viewBox="0 0 16 16" fill="#1DA1F2"><use xlink:href="#icon-推特"></use></svg>`,
        '境外新闻网站': `<svg width="16" height="16" viewBox="0 0 16 16" fill="#DC2626"><use xlink:href="#icon-境外新闻网站"></use></svg>`
      }

      return iconMap[iconName] || `<svg width="16" height="16" viewBox="0 0 16 16" fill="#6B7280"><use xlink:href="#icon-新媒体"></use></svg>`
    },

    // 处理柱状图点击事件
    handleBarClick(params) {
      console.log('点击事件触发:', params) // 调试日志

      // 找到对应的账号数据
      const accountName = params.name
      const sortedData = [...this.chartData].sort((a, b) => a.articleCount - b.articleCount)
      const accountData = sortedData.find(item => item.name === accountName)

      console.log('找到的账号数据:', accountData) // 调试日志

      if (accountData && accountData.userId && accountData.situation) {
        console.log('准备跳转:', accountData.userId, accountData.situation) // 调试日志
        // 跳转到账号详情页
        const routeData = this.$router.resolve({
          path: '/main/archiveDetail',
          query: {
            ukey: accountData.userId,
            situation: accountData.situation
          }
        })
        window.open(routeData.href, '_blank')
      } else {
        console.log('账号信息不完整:', accountData) // 调试日志
        this.$Message.warning('账号信息不完整，无法跳转到详情页')
      }
    },

    // 处理窗口大小变化
    handleResize() {
      if (this.chart) {
        this.chart.resize()
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.fetchData() // 使用模拟数据
    })

    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose()
    }
    window.removeEventListener('resize', this.handleResize)
  }
}
</script>

<style lang="less" scoped>
.active-account-stats {
  position: relative;

  .component-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
    padding-bottom: 8px;
    width:222px;
    height: 36px;
    line-height: 36px;
    position: relative;
    margin:-15px auto 24px auto;
    text-align: center;
    background-image: url("../img/矩形.svg");
  }

  .chart-container {
    .legend {
      display: flex;
      justify-content: center;
  
      gap: 20px;

      .legend-item {
        display: flex;
        align-items: center;
        gap: 6px;

        .legend-color {
          width: 12px;
          height: 12px;
          border-radius: 2px;

          &.gradient-color {
            background: linear-gradient(to right,#26A69A, #4ECDC4 );
          }
        }

        .legend-text {
          font-size: 12px;
          color: #666;
        }
      }
    }

    .bar-chart {
      width: 100%;
      height: 350px;
      cursor: pointer;
    }
  }
}
</style>
