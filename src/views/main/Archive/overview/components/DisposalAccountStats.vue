<template>
  <div class="disposal-account-stats">
    <h4 class="component-title">涉济处置账号</h4>
    
    <div class="stats-content">
      <div v-show="!hasData && !loading" class="empty-container">
        <EmptyState text="暂无数据" description="当前时间范围内没有涉济处置账号数据" height="350px" />
      </div>
      <div v-show="hasData && !loading">
        <!-- 统计表格 -->
        <div class="stats-table">
          <Table
            :columns="tableColumns"
            :data="tableData"
            :loading="loading"
            size="large"
            stripe
            @on-row-click="handleRowClick"
          >
            <template slot-scope="{ row }" slot="platform">
              <div class="platform-cell">
                <svg-icon
                  :icon-class="getPlatformIcon(row.situation)"
                  style="font-size: 16px; margin-right: 5px;"
                />
                <span>{{ row.platformName }}</span>
              </div>
            </template>


          </Table>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <Spin size="large" />
    </div>
  </div>
</template>

<script>
import EmptyState from './EmptyState.vue'
import { buildTimeParams } from '../utils/timeParamsBuilder.js'

export default {
  name: 'DisposalAccountStats',
  components: {
    EmptyState
  },
  props: {
    timeRange: {
      type: Object,
      default: () => ({
        type: 'all',
        startDate: null,
        endDate: null,
        label: '全部'
      })
    }
  },
  data() {
    return {
      loading: false,
      tableColumns: [
        {
          title: '昵称',
          key: 'nickname',
          width: 160,
          ellipsis: true
        },
        {
          title: '所属平台',
          slot: 'platform',
          width: 165
        },
        {
          title: '涉济发文总量',
          key: 'totalArticles',
          width: 160,
          align: 'center'
        },
        {
          title: '涉济负面占比',
          key: 'negativeRatio',
          width: 160,
          align: 'center'
        },
        {
          title: '处置次数',
          key: 'disposalCount',
          width: 160,
          align: 'center',
          sortable: true
        }
      ],
      tableData: []
    }
  },
  computed: {
    hasData() {
      return this.tableData && this.tableData.length > 0
    }
  },
  watch: {
    timeRange: {
      handler() {
        this.fetchData() // 暂时注释掉接口调用
      },
      deep: true
    }
  },
  methods: {
    // 获取数据
    async fetchData() {
      this.loading = true
      try {
        const params = buildTimeParams(this.timeRange)
        // 添加排序参数，按处置次数排序
        params.orderBy = 'deal_num'
        params.limit = '5' // 显示前5个

        // 调用接口获取涉济处置账号统计
        const response = await this.$http.get('/holoArchiveChart/infoList', { params })

        if (response.body.status === 0) {
          this.tableData = this.processApiData(response.body.data) || []
        } else {
          this.tableData = []
        }
      } catch (error) {
        console.error('获取涉济处置账号统计失败:', error)
        // 使用模拟数据
        this.tableData = this.getMockData()
      } finally {
        this.loading = false
      }
    },

    // 处理接口返回的数据
    processApiData(data) {
      if (!data || !Array.isArray(data)) return []

      return data.map(item => ({
        nickname: item.accountName || item.name || '未知账号',
        platform: this.getPlatformKey(item.situation),
        platformName: item.mediumName,
        totalArticles: item.pushNum || 0,
        negativeRatio: this.calculateNegativeRatio(item.negativeNum, item.pushNum),
        disposalCount: item.dealNum || 0,
        situation: item.situation,
        userId: item.userId || item.ukey || null
      }))
    },

    // 计算负面占比
    calculateNegativeRatio(negativeNum, totalNum) {
      if (!totalNum || totalNum === 0) return '0%'
      const ratio = ((negativeNum || 0) / totalNum * 100).toFixed(1)
      return `${ratio}%`
    },

    // 根据situation获取平台key
    getPlatformKey(situation) {
      const platformMap = {
        10: 'weibo',
        20: 'wechat',
        30: 'news',
        31: 'app',
        48: 'douyin',
        50: 'kuaishou',
        60: 'forum',
        80: 'xiaohongshu',
        600: 'toutiao'
      }
      return platformMap[situation] || 'other'
    },

    // 根据situation获取平台名称
    getPlatformName(situation) {
      const nameMap = {
        10: '新浪微博',
        20: '微信公众号',
        30: '媒体网站',
        31: '客户端',
        48: '抖音',
        50: '快手',
        60: '论坛贴吧',
        80: '小红书',
        600: '今日头条'
      }
      return nameMap[situation] || '其他平台'
    },



    // 获取模拟数据 - 被处置最多的5个账号，按处置次数倒序
    getMockData() {
      return [
        {
          nickname: '济南时报',
          platform: 'douyin',
          platformName: '抖音',
          totalArticles: 0,
          negativeRatio: '0%',
          disposalCount: 100,
          situation: 48,
          userId: 'addJcHand_113460'
        },
        {
          nickname: '济南日报网',
          platform: 'toutiao',
          platformName: '今日头条',
          totalArticles: 1,
          negativeRatio: '1%',
          disposalCount: 97,
          situation: 600,
          userId: 'addJcHand_113461'
        },
        {
          nickname: '大众网',
          platform: 'weibo',
          platformName: '新浪微博',
          totalArticles: 0,
          negativeRatio: '3%',
          disposalCount: 95,
          situation: 10,
          userId: 'addJcHand_113462'
        },
        {
          nickname: '济南日报网',
          platform: 'xiaohongshu',
          platformName: '小红书',
          totalArticles: 1,
          negativeRatio: '8%',
          disposalCount: 93,
          situation: 80,
          userId: 'addJcHand_113463'
        },
        {
          nickname: '大众网',
          platform: 'xiaohongshu',
          platformName: '小红书',
          totalArticles: 7,
          negativeRatio: '10%',
          disposalCount: 81,
          situation: 80,
          userId: 'addJcHand_113464'
        }
      ].sort((a, b) => b.disposalCount - a.disposalCount) // 按处置次数倒序
    },



    // 获取平台图标
    getPlatformIcon(situation) {
      const iconMap = {
        30: "新闻网站",
        31: "新闻APP",
        10: "微博",
        20: "微信公众号",
        61: "论坛",
        62: "贴吧",
        170: "知乎",
        41: "论坛跟帖",
        51: "贴吧跟帖",
        80: "小红书",
        48: "视频",
        50: "视频",
        110: "火山",
        188: "视频",
        199: "视频",
        30: "新闻",
        31: "新闻app",
        10: "微博",
        20: "微信公号",
        80: "小红书",
        60: "论坛",
        61: "论坛",
        62: "贴吧",
        170: "知乎",
        230: "自媒体",
        300: "视频300",
        200: "视频",
        320: "新闻网站",
        400: "境外新闻网站",
        501: "Facebook",
        502: "推特",
        600: "今日头条",
      }
      return iconMap[situation] || '新媒体'
    },

    // 处理表格行点击事件
    handleRowClick(row, index) {
      if (row.userId && row.situation) {
        // 跳转到账号详情页
        const routeData = this.$router.resolve({
          path: '/main/archiveDetail',
          query: {
            ukey: row.userId,
            situation: row.situation
          }
        })
        window.open(routeData.href, '_blank')
      } else {
        this.$Message.warning('账号信息不完整，无法跳转到详情页')
      }
    }

  },
  mounted() {
    this.fetchData() // 使用模拟数据
  }
}
</script>

<style lang="less" scoped>
.disposal-account-stats {
  position: relative;

  .component-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
    padding-bottom: 8px;
    width:222px;
    height: 36px;
    line-height: 36px;
    position: relative;
    margin:-15px auto 24px auto;
    text-align: center;
    background-image: url("../img/矩形.svg");
  }

  .stats-content {
    .stats-table {
      width: 100%;
      .platform-cell {
        display: flex;
        align-items: center;
      }
    }
  }

  .loading-container {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 6px;
  }
}

// 表格样式优化
:deep(.ivu-table-wrapper){

}
:deep(.ivu-table) {
  .ivu-table-header{
     th{
      background:none!important;
      border:none;
    }
  }
  .ivu-table-tbody {
   
    tr:nth-child(odd) td {
      background-color: rgba(235,237,248,0.47) !important;
    }
    tr td{
      border:none;
      background-color: rgba(245,245,245,0.47) !important;

    }
    tr:hover td {
      background-color: #f8f9fa !important;
      cursor: pointer;
    }
  }
  
  .ivu-table-cell {
    padding: 8px 12px;
  }
}
</style>
