<template>
  <div class="stat-time-period">
    <div class="time-period-container">
      <!-- 左侧：统计时段显示 -->
      <div class="time-period-display">
      <svg-icon icon-class="榜单-时间" style="width:20px;height:20px" />
        <span class="label">统计时段：</span>
        <span class="time-range">{{ displayTimeRange }}</span>
      </div>

      <!-- 右侧：时间范围选择按钮 -->
      <div class="time-period-controls">
        <svg-icon icon-class="榜单-时间" style="width:20px;height:20px"/>
        <span class="label">统计时段：</span>
        
        <ButtonGroup class="time-buttons">
          <Button 
            :type="currentType === 'all' ? 'primary' : 'default'"
            size="small"
            @click="selectTimeRange('all')"
          >
            全部
          </Button>
          <Button 
            :type="currentType === 'day' ? 'primary' : 'default'"
            size="small"
            @click="selectTimeRange('day')"
          >
            近一日
          </Button>
          <Button 
            :type="currentType === 'week' ? 'primary' : 'default'"
            size="small"
            @click="selectTimeRange('week')"
          >
            近一周
          </Button>
          <Button 
            :type="currentType === 'month' ? 'primary' : 'default'"
            size="small"
            @click="selectTimeRange('month')"
          >
            近一月
          </Button>
          <Button 
            :type="currentType === 'custom' ? 'primary' : 'default'"
            size="small"
            @click="showCustomDatePicker"
          >
            自定义
          </Button>
        </ButtonGroup>
      </div>
    </div>

    <!-- 自定义时间选择弹框 -->
    <Modal
      v-model="showDatePicker"
      title="选择时间范围"
      width="400"
      @on-ok="confirmCustomDate"
      @on-cancel="cancelCustomDate"
    >
      <div class="custom-date-picker">
        <div class="date-input-group">
          <label>时间范围：</label>
          <DatePicker
            v-model="customDateRange"
            type="daterange"
            placeholder="选择日期范围"
            style="width: 280px"
            :options="datePickerOptions"
            format="yyyy-MM-dd"
            separator=" 至 "
          />
        </div>
      </div>
    </Modal>
  </div>
</template>

<script>
// import { format } from 'date-fns'

export default {
  name: 'StatTimePeriod',
  data() {
    return {
      currentType: 'all', // all, day, week, month, custom
      currentTimeRange: {
        type: 'all',
        startDate: null,
        endDate: null,
        label: '全部'
      },
      showDatePicker: false,
      customDateRange: [], // 日期范围数组 [startDate, endDate]
      datePickerOptions: {
        disabledDate: (date) => {
          // 禁用未来日期
          return date && date.valueOf() > Date.now()
        }
      }
    }
  },
  computed: {
    // 显示的时间范围文本
    displayTimeRange() {
      if (this.currentType === 'custom' && this.currentTimeRange.startDate && this.currentTimeRange.endDate) {
        const startStr = this.formatDateString(this.currentTimeRange.startDate)
        const endStr = this.formatDateString(this.currentTimeRange.endDate)
        return `${startStr} 至 ${endStr}`
      }
      return this.currentTimeRange.label
    }
  },
  methods: {
    // 选择时间范围
    selectTimeRange(type) {
      this.currentType = type
      const now = new Date()
      let timeRange = {
        type: type,
        startDate: null,
        endDate: null,
        label: '全部'
      }

      switch (type) {
        case 'all':
          timeRange.label = '全部'
          break
        case 'day':
          timeRange.startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000)
          timeRange.endDate = now
          timeRange.label = '近一日'
          break
        case 'week':
          timeRange.startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
          timeRange.endDate = now
          timeRange.label = '近一周'
          break
        case 'month':
          timeRange.startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
          timeRange.endDate = now
          timeRange.label = '近一月'
          break
      }

      this.currentTimeRange = timeRange
      this.$emit('change', timeRange)
    },

    // 显示自定义日期选择器
    showCustomDatePicker() {
      // 如果已有自定义时间范围，则预填充
      if (this.currentTimeRange.startDate && this.currentTimeRange.endDate) {
        this.customDateRange = [this.currentTimeRange.startDate, this.currentTimeRange.endDate]
      } else {
        this.customDateRange = []
      }
      this.showDatePicker = true
    },

    // 确认自定义日期
    confirmCustomDate() {
      if (!this.customDateRange || this.customDateRange.length !== 2) {
        this.$Message.warning('请选择完整的时间范围')
        return
      }

      const [startDate, endDate] = this.customDateRange

      if (!startDate || !endDate) {
        this.$Message.warning('请选择完整的时间范围')
        return
      }

      // 设置开始时间为当天的00:00:00
      const adjustedStartDate = new Date(startDate)
      adjustedStartDate.setHours(0, 0, 0, 0)

      // 设置结束时间为当天的23:59:59
      const adjustedEndDate = new Date(endDate)
      adjustedEndDate.setHours(23, 59, 59, 999)

      this.currentType = 'custom'
      this.currentTimeRange = {
        type: 'custom',
        startDate: adjustedStartDate,
        endDate: adjustedEndDate,
        label: '自定义'
      }

      this.showDatePicker = false
      this.$emit('change', this.currentTimeRange)
    },

    // 取消自定义日期
    cancelCustomDate() {
      this.showDatePicker = false
    },

    // 格式化日期字符串
    formatDateString(date) {
      const d = new Date(date)
      const year = d.getFullYear()
      const month = String(d.getMonth() + 1).padStart(2, '0')
      const day = String(d.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}`
    }
  },
  mounted() {
    // 默认选择全部
    this.selectTimeRange('all')
  }
}
</script>

<style lang="less" scoped>
.stat-time-period {
 
  
  .time-period-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: white;
     border-bottom: 1px solid #F2F2F2;


    .time-period-display,
    .time-period-controls {
      display: flex;
      align-items: center;
      gap: 8px;

      .label {
        font-size: 14px;
        color: #666;
      }

      .time-range {
        font-size: 14px;
        font-weight: 500;
        color: #2d8cf0;
      }
    }

    .time-buttons {
      margin-left: 10px;
    }
  }

  .custom-date-picker {
    .date-input-group {
      display: flex;
      align-items: center;
      margin-bottom: 15px;

      label {
        width: 80px;
        font-size: 14px;
        color: #333;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .stat-time-period .time-period-container {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }
}
</style>
