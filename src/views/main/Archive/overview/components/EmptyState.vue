<template>
  <div class="empty-state" :style="{ height: height }">
    <div class="empty-content">
      <div class="empty-icon">
        <svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M32 8C18.745 8 8 18.745 8 32s10.745 24 24 24 24-10.745 24-24S45.255 8 32 8zm0 44c-11.046 0-20-8.954-20-20s8.954-20 20-20 20 8.954 20 20-8.954 20-20 20z" fill="#D9D9D9"/>
          <path d="M32 16c-8.837 0-16 7.163-16 16s7.163 16 16 16 16-7.163 16-16-7.163-16-16-16zm0 28c-6.627 0-12-5.373-12-12s5.373-12 12-12 12 5.373 12 12-5.373 12-12 12z" fill="#D9D9D9"/>
          <circle cx="32" cy="32" r="4" fill="#D9D9D9"/>
        </svg>
      </div>
      <div class="empty-text">{{ text }}</div>
      <div v-if="description" class="empty-description">{{ description }}</div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'EmptyState',
  props: {
    // 显示的主要文本
    text: {
      type: String,
      default: '暂无数据'
    },
    // 描述文本
    description: {
      type: String,
      default: ''
    },
    // 容器高度
    height: {
      type: String,
      default: '200px'
    }
  }
}
</script>

<style lang="less" scoped>
.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  min-height: 200px;
  
  .empty-content {
    text-align: center;
    
    .empty-icon {
      margin-bottom: 16px;
      opacity: 0.6;
      
      svg {
        display: block;
        margin: 0 auto;
      }
    }
    
    .empty-text {
      font-size: 16px;
      color: #999999;
      line-height: 24px;
      margin-bottom: 8px;
      font-weight: 400;
    }
    
    .empty-description {
      font-size: 14px;
      color: #CCCCCC;
      line-height: 20px;
    }
  }
}

// 小尺寸样式
.empty-state.small {
  min-height: 120px;
  
  .empty-content {
    .empty-icon svg {
      width: 48px;
      height: 48px;
    }
    
    .empty-text {
      font-size: 14px;
    }
    
    .empty-description {
      font-size: 12px;
    }
  }
}
</style>
