<template>
  <div class="local-website-stats">
    <div class="stats-header">
      <div class="total-stats">
        <div class="stats-icon">
          <svg-icon icon-class="网站总量" style="font-size: 40px; color: #4A90E2;" />
        </div>
        <div class="stats-content">
          <div class="stats-number">{{ formatNumber(totalWebsiteCount) }}</div>
          <div class="stats-label">属地网站总量</div>
        </div>
      </div>
    </div>

    <div class="chart-container" v-loading="loading">
      <div ref="barChart" class="bar-chart"></div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { buildTimeParams } from '../utils/timeParamsBuilder.js'

export default {
  name: 'LocalWebsiteStats',
  props: {
    timeRange: {
      type: Object,
      default: () => ({
        type: 'all',
        startDate: null,
        endDate: null,
        label: '全部'
      })
    }
  },
  data() {
    return {
      loading: false,
      chart: null,
      totalWebsiteCount: 0,
      chartData: []
    }
  },
  watch: {
    timeRange: {
      handler() {
        this.fetchData() // 启用接口调用
      },
      deep: true
    }
  },
  methods: {
    // 获取数据
    async fetchData() {
      this.loading = true
      try {
        // 构建时间参数
        const params = buildTimeParams(this.timeRange)

        // 调用接口获取属地网站统计
        const response = await this.$http.get('/holoArchiveChart/getWebsiteDistrict', { params })

        if (response.body.status === 0) {
          const data = response.body.data
          this.totalWebsiteCount = data.total || 0
          this.chartData = this.processApiData(data.list || [])
          this.updateChart()
        } else {
          console.error('属地网站统计接口错误:', response.body.message)
          this.$Message.error(response.body.message || '获取属地网站统计失败')
          // 使用默认数据
          this.chartData = []
          this.updateChart()
        }
      } catch (error) {
        console.error('获取属地网站统计失败:', error)
        this.$Message.error('获取属地网站统计失败')
        // 使用默认数据
        this.chartData = []
        this.updateChart()
      } finally {
        this.loading = false
      }
    },

    // 处理API返回的数据
    processApiData(apiData) {
      // 确保按照设计要求的顺序排列16个区县
      const districtOrder = [
        '历下区', '市中区', '槐荫区', '天桥区',
        '历城区', '长清区', '章丘区', '济阳区',
        '莱芜区', '钢城区', '平阴县', '商河县',
        '高新区', '南部山区', '起步区', '其他'
      ]

      // 创建数据映射
      const dataMap = {}
      apiData.forEach(item => {
        dataMap[item.name] = item.num
      })

      // 按照指定顺序返回数据
      return districtOrder.map(name => ({
        name: name,
        value: dataMap[name] || 0
      }))
    },

    // 格式化数字
    formatNumber(num) {
      if (num >= 10000) {
        return (num / 10000).toFixed(1) + '万'
      }
      return num.toLocaleString()
    },

    // 初始化图表
    initChart() {
      this.chart = echarts.init(this.$refs.barChart)
      this.updateChart()
    },

    // 更新图表
    updateChart() {
      if (!this.chart || !this.chartData.length) return

      const categories = this.chartData.map(item => item.name)
      const values = this.chartData.map(item => item.value)
      const maxValue = Math.max(...values)

      const option = {
        grid: {
          top: 20,
          right: 30,
          bottom: 60,
          left: 60
        },
        xAxis: {
          type: 'category',
          data: categories,
          axisLabel: {
            rotate: 0,
            fontSize: 10,
            color: '#666',
            interval: 0,
            overflow: 'truncate',
            width: 40
          },
          axisLine: {
            lineStyle: {
              color: '#e8e8e8'
            }
          },
          axisTick: {
            show: false
          }
        },
        yAxis: {
          type: 'value',
          max: Math.ceil(maxValue * 1.1),
          axisLabel: {
            fontSize: 12,
            color: '#666'
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          splitLine: {
            lineStyle: {
              color: '#f0f0f0',
              type: 'dashed'
            }
          }
        },
        series: [{
          type: 'bar',
          data: values.map((value) => ({
            value,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#4A90E2' },
                { offset: 1, color: '#357ABD' }
              ])
            }
          })),
          barWidth: '60%',
          label: {
            show: true,
            position: 'top',
            fontSize: 10,
            color: '#333'
          }
        }],
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function(params) {
            const data = params[0]
            return `${data.name}<br/>网站数量: ${data.value} 个`
          }
        },
        dataZoom: [
          {
            type: 'slider',
            show: true,
            xAxisIndex: [0],
            start: 0,
            end: 100,
            bottom: 10,
            height: 20,
            handleSize: 8,
            showDetail: false
          }
        ]
      }

      this.chart.setOption(option)
    },

    // 处理窗口大小变化
    handleResize() {
      if (this.chart) {
        this.chart.resize()
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
      this.fetchData() // 启用接口调用
    })

    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose()
    }
    window.removeEventListener('resize', this.handleResize)
  }
}
</script>

<style lang="less" scoped>
.local-website-stats {
  position: relative;

  .component-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    padding-left: 8px;
    border-left: 4px solid #4A90E2;
  }

  .stats-header {


    .total-stats {
      display: flex;
      align-items: center;
      gap: 16px;
      background: #F5FEFF;
      border-radius: 8px;
      padding: 14px 20px;
      justify-content: center;
      

      .stats-icon {
        flex-shrink: 0;
      }

      .stats-content {
        position: relative;
        padding-left:15px;
        &::before{
          content:"";
          width: 3px;
          height:45px;
          background: #5482EA;
          display: block;
          position: absolute;
          left:0;
          top:5px;
          border-bottom:22px solid #C3DAF8;
        }
        .stats-number {
          font-weight: 600;
          font-size: 20px;
          color: #5482EA;
          line-height: 28px;
          text-align: left;
        }

        .stats-label {
          font-weight: 400;
          font-size: 16px;
          color: #000000;
          line-height: 22px;
        }
      }
    }
  }

  .chart-container {
    background: #fff;
    border-radius: 8px;
    
    

    .bar-chart {
      width: 100%;
      height: 350px;
    }
  }
}
</style>
