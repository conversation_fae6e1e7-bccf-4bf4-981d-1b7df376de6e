/**
 * 时间参数构建工具
 * 统一管理档案概览页面中所有组件的时间参数生成逻辑
 */

/**
 * 构建时间参数
 * @param {Object} timeRange - 时间范围对象
 * @param {string} timeRange.type - 时间类型: 'all', 'recent1', 'recent7', 'recent30', 'custom'
 * @param {Date|string} timeRange.startDate - 开始日期 (仅在custom类型时使用)
 * @param {Date|string} timeRange.endDate - 结束日期 (仅在custom类型时使用)
 * @returns {Object} 接口所需的时间参数
 */
export function buildTimeParams(timeRange) {
  const params = {}
  
  if (!timeRange || timeRange.type === 'all') {
    // 全部时间，不传参数
    return params
  }
  
  if (timeRange.type === 'custom') {
    // 自定义时间范围
    if (timeRange.startDate) {
      params.startTime = formatDateTime(timeRange.startDate, '00:00:00')
    }
    if (timeRange.endDate) {
      params.endTime = formatDateTime(timeRange.endDate, '23:59:59')
    }
    params.dayNum = '-1' // 自定义时间标识
  } else {
    // 预设时间范围
    const dayNumMap = {
      'day': '1',    // 近一天
      'week': '6',    // 近一周  
      'month': '30'   // 近一月
    }
    params.dayNum = dayNumMap[timeRange.type] || '1'
  }
  
  return params
}

/**
 * 格式化日期时间
 * @param {Date|string} date - 日期
 * @param {string} time - 时间 (格式: HH:mm:ss)
 * @returns {string} 格式化后的日期时间字符串 (格式: yyyy-MM-dd HH:mm:ss)
 */
export function formatDateTime(date, time) {
  const d = new Date(date)
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  return `${year}-${month}-${day} ${time}`
}

/**
 * 格式化日期
 * @param {Date|string} date - 日期
 * @returns {string} 格式化后的日期字符串 (格式: yyyy-MM-dd)
 */
export function formatDate(date) {
  const d = new Date(date)
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

/**
 * 获取时间范围描述
 * @param {Object} timeRange - 时间范围对象
 * @returns {string} 时间范围的文字描述
 */
export function getTimeRangeDescription(timeRange) {
  if (!timeRange || timeRange.type === 'all') {
    return '全部时间'
  }
  
  const typeMap = {
    'day': '1',    // 近一天
    'week': '6',    // 近一周  
    'month': '30'   // 近一月
  }
  
  if (timeRange.type === 'custom') {
    if (timeRange.startDate && timeRange.endDate) {
      return `${formatDate(timeRange.startDate)} 至 ${formatDate(timeRange.endDate)}`
    }
    return '自定义时间'
  }
  
  return typeMap[timeRange.type] || '未知时间范围'
}
