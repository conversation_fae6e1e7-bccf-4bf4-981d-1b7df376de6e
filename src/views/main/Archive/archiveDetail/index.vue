<template>
  <div class="account-info-container">
    <div class="account-header">
         <div class="left">
                <div class="account-baseinfo">
                    <platformIcon
                        :size="70"
                        v-if="countBaseInfo.situation"
                        :id="countBaseInfo.situation"></platformIcon>

                    <div class="platform-name">{{ handleSource }}</div>
                </div>
                <div class="account-info">
                    <div class="account-title flex sb">
                        <div class="account-title-left">
                            <span
                                class="account-name"
                                :title="countBaseInfo.accountName">
                                {{ countBaseInfo.accountName || "" }}
                            </span>

                            <!-- <span class="btn-action btn-orange" v-if="phoneTip == 0" @click="openApplyModal(0)">
                <svg-icon icon-class="手机号申请-橘黄" style="width: 15px; height: 15px;margin-right: 2px;" />申请补录个人信息
              </span> -->
                            <span
                                class="btn-action btn-orange"
                                @click="openXxwsModal()"
                                v-if="!$route.query.mkey&&hasUserId==1">
                                <svg-icon
                                    icon-class="user-wsxx"
                                    style="
                                        width: 15px;
                                        height: 15px;
                                        margin-right: 2px;
                                    " />信息完善
                            </span>
                            <!-- 二维码图标 -->
                            <span
                                v-if="homePageUrl && !$route.query.mkey && hasUserId==1"
                                class="qr-code-icon"
                                style="margin-left: 8px;">
                                <Tooltip
                                    placement="bottom"
                                    :content="'主页链接二维码'"
                                    :transfer="true">
                                    <div slot="content" class="qr-code-tooltip">
                                        <img
                                            :src="generateQRCode(homePageUrl)"
                                            alt="主页链接二维码"
                                            style="width: 200px; height: 200px; display: block;"
                                            @error="$event.target.style.display='none'"
                                        />
                                        <p style="text-align: center; margin-top: 8px; font-size: 12px; color: #666;">
                                            {{ homePageUrl }}
                                        </p>
                                    </div>
                                    <svg-icon
                                        icon-class="二维码"
                                        style="
                                            width: 16px;
                                            height: 16px;
                                            color: #5585ec;
                                            cursor: pointer;
                                        " />
                                </Tooltip>
                            </span>
                            <span
                            v-if="$route.query.mkey||hasUserId==0"
                                class="btn-action btn-blue"
                                @click="openAddxxModal()"
                               >
                                <svg-icon
                                    icon-class="方形-添加"
                                    style="
                                        width: 15px;
                                        height: 15px;
                                        margin-right: 2px;
                                    " />添加此账号
                            </span>
                        </div>
                        <div>
                            <DatePicker
                                v-if="dayNum === '-99'"
                                type="datetimerange"
                                placeholder="Select date and time"
                                style="width: 300px"
                                @on-change="dateChange"></DatePicker>
                            <Select v-model="dayNum" style="width: 100px">
                                <Option
                                    v-for="item in dayList"
                                    :value="item.value"
                                    :key="item.value"
                                    >{{ item.label }}</Option
                                >
                            </Select>
                        </div>
                    </div>
                    <div class="account-info-box text" v-if="$route.query.mkey||hasUserId==0">
                      <svg-icon icon-class="nouserId" />
                      <span>该账号暂未纳入账号库，请点击“添加此账号”进行信息补充，提交后该账号进入账号库。</span>

                    </div>
                    <div class="account-info-box" v-else>
                        <div class="account_div">
                            <div class="lable">
                                <div>账 号 信 息</div>
                            </div>

                            <div class="info-list">
                                <div class="info-item">
                                    <span class="label">账号ID：</span>
                                    <span
                                        class="value"
                                        :title="fullUserId"
                                        :class="{
                                            'show-ellipsis': userIdLength > 10,
                                        }">
                                        {{ displayedUserId }}
                                    </span>
                                </div>
                                <div class="info-item">
                                    <span class="label">注册地：</span>
                                    <span class="value">{{
                                        countBaseInfo.registerAddress || "-"
                                    }}</span>
                                </div>
                                <div class="info-item">
                                    <span class="label">粉丝数：</span>
                                    <span class="value">{{
                                        countBaseInfo.fansCount || "-"
                                    }}</span>
                                </div>
                                <div class="info-item">
                                    <span class="label">关注数：</span>
                                    <span class="value">{{
                                        countBaseInfo.followCount || "-"
                                    }}</span>
                                </div>
                                <div class="info-item">
                                    <span class="label">主页链接：</span>
                                    <span
                                        class="value "
                                        :title="countBaseInfo.url"
                                        >{{ countBaseInfo.url || "-" }}</span
                                    >
                                </div>
                            </div>
                        </div>
                        <div class="user_div">
                            <div class="lable">人 物 信 息</div>
                            <div
                                class="info-list user_tip_box"
                                v-if="
                                   phoneTip!=3
                                ">
                                <div class="user_tip">
                                    <svg-icon icon-class="user-tanhao" />
                                    <span>暂无查看权限</span>
                                </div>
                            </div>



                            <div class="info-list" v-else>
                                <div class="info-item">
                                    <span class="label">姓名：</span>
                                    <span class="value">{{
                                        countBaseInfo.name || "-"
                                    }}</span>
                                </div>
                                <div class="info-item">
                                    <span class="label">注册邮箱：</span>
                                    <span
                                        class="value ellipsis"
                                        :title="countBaseInfo.email"
                                        >{{ countBaseInfo.email || "-" }}</span
                                    >
                                </div>
                                <div class="info-item">
                                    <span class="label">性别：</span>
                                    <span class="value">{{
                                        countBaseInfo.sex || "-"
                                    }}</span>
                                </div>
                                <div class="info-item">
                                    <span class="label">历史邮箱：</span>
                                    <span
                                        class="value ellipsis"
                                        :title="countBaseInfo.oldEmail"
                                        >{{ countBaseInfo.oldEmail || "-" }}</span
                                    >
                                </div>

                                <div class="info-item">
                                    <span class="label">出生日期：</span>
                                    <span class="value">{{
                                        countBaseInfo.date || "-"
                                    }}</span>
                                </div>

                                <div class="info-item">
                                    <span class="label">工作单位：</span>
                                    <span
                                        class="value ellipsis"
                                        :title="countBaseInfo.workUnit"
                                        >{{ countBaseInfo.workUnit || "-" }}</span
                                    >
                                </div>

                                <div class="info-item">
                                    <span class="label">身份证号：</span>
                                    <span
                                        class="value ellipsis"
                                        :title="countBaseInfo.idCard"
                                        >{{ countBaseInfo.idCard || "-" }}</span
                                    >
                                </div>

                                <div class="info-item">
                                    <span class="label">住址：</span>
                                    <span
                                        class="value"
                                        :title="countBaseInfo.address"
                                        >{{
                                            countBaseInfo.address || "-"
                                        }}</span
                                    >
                                </div>

                                <div class="info-item">
                                  <svg-icon
                                    icon-class="重点"
                                    style="width: 13px; height: 16px;"
                                    class="icon"
                                  />
                                    <span class="label">手机号：</span>
                                    <span class="value">{{
                                        countBaseInfo.phone || "-"
                                    }}</span>
                                </div>
                                <div class="info-item">
                                    <span class="label">历史手机号：</span>
                                    <span class="value ellipsis" :title="countBaseInfo.oldPhone">{{
                                        countBaseInfo.oldPhone || "-"
                                    }}</span>
                                </div>
                               
                                 

                                 <div class="info-item">
                                    <span class="label">备注：</span>
                                    <span
                                        class="value ellipsis"
                                        :title="countBaseInfo.remark"
                                        >{{ countBaseInfo.remark || "-" }}</span
                                    >
                                </div>
                                <!-- <div class="info-item">
                <span class="label">
                  <Icon class="label-icon" type="ios-phone-portrait" />
                  注册手机：</span
                >
                <span class="value">{{ countBaseInfo.phone || "-" }}</span>
              </div>
              <div class="info-item">
                <span class="label">
                  <Icon class="label-icon" type="ios-time" />
                  注册时间：</span
                >
                <span class="value">
                  {{
                    countBaseInfo.registerTime
                      ? moment(countBaseInfo.registerTime).format("YYYY-MM-DD")
                      : "-"
                  }}</span
                >
              </div>
              <div class="info-item">
                <span class="label">
                  <Icon class="label-icon" type="ios-phone-portrait" />
                  历史手机：</span
                >
                <span class="value">{{ countBaseInfo.oldPhone || "-" }}</span>
              </div>
              <div class="info-item">
                <span class="label">
                  <Icon class="label-icon" type="ios-mail-outline" />
                  历史邮箱：</span
                >
                <span class="value">{{ countBaseInfo.oldEmail || "-" }}</span>
              </div> -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
      <div class="right">
        <div class="stats-left">
          <div class="stat-card color_qing flex sb" style="align-items: center;">
            <div
              class="stat-header flex"
              style="align-items: center; margin-bottom: 0px;"
            >
            <svg-icon
              icon-class="发稿统计"
              style="width: 39px; height: 36px;"
              class="icon"
            />
            <svg-icon
              icon-class="发稿统计"
              style="height: 60px;width: 75px;"
              class="bg"
            />
              <span
                style="line-height: 20px; font-size: 18px; font-weight: 700;"
              >
                账号评分
              </span>
              <Tooltip
                placement="bottom-end"
                transfer
                max-width="360"
                :offset="12"
              >
                <Icon class="" size="14px" type="ios-help-circle" />
                <div slot="content" class="tooltip-content">
                  <p class="bluetext">
                    账号评分：为0~100的整数，数字越小该账号越敏感。
                  </p>
                  <p class="bluetext">初始分为100分，最低分为0分</p>
                  <p>负面信息：每1条负面信息，得分-0.5</p>
                  <p>打标信息：每1条打标信息，得分-2</p>
                  <p>提示单引用：每1条提示单引用信息，得分-5</p>
                  <p>要报引用：每1条要报引用信息，得分-5</p>
                  <p>
                    总得分=100-负面信息分-打标信息分-提示单引用分-要报引用分
                  </p>
                </div>
              </Tooltip>
            </div>
            <div class="stat-content flex">
              <span class="number" style="font-weight: 700;">{{
                countInfo.score || 0
              }}</span>
            </div>
          </div>
          <div class="stat-card color_qunqing">
            <div class="stat-header">涉济发文总量</div>
            <svg-icon
              icon-class="文本"
              style="width: 39px; height: 36px;"
              class="icon"
            />
            <svg-icon
              icon-class="文本"
              style="height: 60px;width: 75px;"
              class="bg"
            />
            <div class="stat-content">
              <span class="number">{{ countBaseInfo.pushNum || 0 }}</span>
              <div class="sub-info">
                <span>负面占比</span>
                <span class="percentage">
                  {{
                    countBaseInfo.negativeNum
                      ? countBaseInfo.negativeNum / countBaseInfo.pushNum ==
                          0 ||
                        countBaseInfo.negativeNum / countBaseInfo.pushNum == 1
                        ? parseFloat(
                            (countBaseInfo.negativeNum /
                              countBaseInfo.pushNum) *
                              100
                          ).toFixed(0) + "%"
                        : parseFloat(
                            (countBaseInfo.negativeNum /
                              countBaseInfo.pushNum) *
                              100
                          ).toFixed(2) + "%"
                      : "0%"
                  }}</span
                >
              </div>
            </div>
          </div>

          <div class="stat-card color_cheng">
            <div class="stat-header">打标信息量</div>
            <svg-icon
              icon-class="文本2"
              style="width: 39px; height: 36px;"
              class="icon"
            />
            <svg-icon
              icon-class="文本2"
              style="height: 60px;width: 75px;"
              class="bg"
            />
            <div class="stat-content">
              <span class="number">{{ countInfo.allTotal || 0 }}</span>
              
            </div>
          </div>

          <div class="stat-card color_blue">
            <div class="stat-header">删除信息量</div>
            <svg-icon
              icon-class="文章转载"
              style="width: 39px; height: 36px;"
              class="icon"
            />
            <svg-icon
              icon-class="文章转载"
              style="height: 60px;width: 75px;"
              class="bg"
            />
            <div class="stat-content">
              <span class="number">{{ countInfo.delTotal || 0 }}</span>
              
              <!-- style="width: 35px; height: 35px;" -->
            </div>
          </div>

          <div class="stat-card color_ju">
            <div class="stat-header">提示单引用量</div>
            <svg-icon
              icon-class="阅读记录"
              style="width: 45px; height: 38px;"
              class="icon"
            />
            <svg-icon
              icon-class="阅读记录"
              style="height: 60px;width: 75px;"
              class="bg"
            />
            <div class="stat-content">
              <span class="number">{{ countInfo.promptTotal || 0 }}</span>
             
            </div>
          </div>

          <div class="stat-card color_green">
            <div class="stat-header">要报引用量</div>
            <svg-icon
              icon-class="AK-MN_互动"
              style="width: 45px; height: 38px;"
              class="icon"
            />
            <svg-icon
              icon-class="AK-MN_互动"
              style="height: 60px;width: 75px;"
              class="bg"
            />
            <div class="stat-content">
              <span class="number">{{ countInfo.periodsTotal || 0 }}</span>
              
            </div>
          </div>
        </div>
        <!-- <div class="stats-right">
          <div class="stat-card purple">
            <div class="stat-header">
              <span>账号评分</span>
              <Tooltip
                placement="bottom-end"
                transfer
                max-width="360"
                :offset="12"
              >
                <Icon class="icon" type="ios-help-circle" />
                <div slot="content" class="tooltip-content">
                  <p class="bluetext">
                    账号评分：为0~100的整数，数字越小该账号越敏感。
                  </p>
                  <p class="bluetext">初始分为100分，最低分为0分</p>
                  <p>负面信息：每1条负面信息，得分-0.5</p>
                  <p>打标信息：每1条打标信息，得分-2</p>
                  <p>提示单引用：每1条提示单引用信息，得分-5</p>
                  <p>要报引用：每1条要报引用信息，得分-5</p>
                  <p>
                    总得分=100-负面信息分-打标信息分-提示单引用分-要报引用分
                  </p>
                </div>
              </Tooltip>
            </div>
            <div class="stat-content">
              <span class="number">{{ countInfo.score || 0 }}</span>
            </div>
          </div>
        </div> -->
      </div>
    </div>
    <div class="content-wrapper">
      <Tabs v-model="tabSelect">
        <TabPane label="近期发文" name="1"></TabPane>
        <TabPane label="统计分析" name="2"></TabPane>
        <TabPane label="信息处置" name="4"></TabPane>
        <TabPane label="关联账号" name="3"></TabPane>
        <TabPane label="信息交流" name="8"></TabPane>
        <TabPane label="操作日志" name="7"></TabPane>
        
        <!-- <TabPane label="发文轨迹" name="5"></TabPane> -->
      </Tabs>
      <StatisticalAnalysis
        v-if="tabSelect === '2'"
        :dayNum="dayNum"
        :mediumName="countBaseInfo.mediumName"
        :userAccount="countBaseInfo.accountName"
        :dateList="dateList"
        :userId="countBaseInfo.userId"
      />
      <!-- <div class="select" style="padding-right: 216px">
                <div style="display: flex; justify-content: space-between">
                    <div>
                        <div style="margin-bottom: 12px">
                            <timeSelect
                                :dataList="timeList"
                                @changeTime="handleTime"
                                name="时间"
                                :selectIds="timeId"
                            />
                        </div>
                    </div>
                    <div>
                        <searchInput @changeSearch="handleSearch" />
                    </div>
                </div>
            </div> -->

      <div class="content_box" v-if="tabSelect === '1'">
        <div class="header">
          <div class="operation">
            <Checkbox
              :indeterminate="indeterminate"
              :value="checkAll"
              @click.prevent.native="handleCheckAll"
              >全选
            </Checkbox>
            
          </div>
          <div class="operation_right">
            <!-- <Select
              v-model="timeId"
              style="width: 100px;"
              @on-change="handleSearch"
            >
              <Option
                v-for="(item, index) in timeList"
                :key="index"
                :value="item.id"
                >{{ item.title }}</Option
              >
            </Select> -->
            <sorts :dataList="orderByTypeList" @changeSort="changeSort"></sorts>
            <!-- <Select v-model="params.orderByType" style="width: 200px;" @on-change="handleSearch">
                            <Option value="1">按时间倒序</Option>
                            <Option value="2">按时间正序</Option>
                        </Select> -->
            <Input
              v-model="params.keyword"
              placeholder="请输入关键词"
              search
              style="width: 300px;"
              @on-search="handleSearch"
            >
              <Select
                v-model="params.searchPosition"
                slot="prepend"
                style="width: 80px;"
              >
                <Option value="1">全文</Option>
                <Option value="2">标题</Option>
                <Option value="3">正文</Option>
              </Select></Input
            >
            <div class="btn" @click="derive">
                            <svg-icon icon-class="信息库-导出" />
                            批量导出
                        </div>
          </div>
        </div>
        <div class="list">
          <Spin v-show="loading" fix>
            <Icon
              type="ios-loading"
              size="18"
              class="demo-spin-icon-load"
            ></Icon>
            <div>Loading</div>
          </Spin>
          <no-data v-show="total === 0 && !loading" />
          <CheckboxGroup
            v-model="checkAllGroup"
            @on-change="checkAllGroupChange"
          >
            <MonitoringInfoList
              v-for="(item, index) in tableData"
              :key="item.mkey"
              :data="item"
              :index="index"
              :pageNo="pageNo"
              :pageSize="pageSize"
              @drawerChange="openDrawer"
            >
              <template v-slot:controls>
                <ListControls :data="item" :btnStatus="btnStatus" />
              </template>
            </MonitoringInfoList>
          </CheckboxGroup>
        </div>
        <Page
          v-show="total > 0 && !loading"
          :total="total"
          show-elevator
          show-total
          show-sizer
          style="position: relative;"
          :current="pageNo"
          :page-size="pageSize"
          @on-change="pageNoChange"
          @on-page-size-change="pageSizeChange"
        />
      </div>
      <RelatesInformation v-if="tabSelect === '3'" :infoData="countBaseInfo" />
      <informationProce v-if="tabSelect === '4'" :dayNum="dayNum" :dateList="dateList"  :infoData="countBaseInfo" />
      <operationLog v-if="tabSelect === '7'" :infoData="countBaseInfo" />
      <InfoExchange v-if="tabSelect === '8'" :userId="countBaseInfo.userId!=''?countBaseInfo.userId: this.$route.query.ukey||''" />
      <!-- <postTrajectory v-if="tabSelect === '5'" :dayNum="dayNum" :dateList="dateList"  :infoData="countBaseInfo" /> -->
    </div>
    <!--    抽屉-->
    <Drawer v-model="drawer" :closable="false" width="750">
      <component :is="componentId" :data="drawerData" />
    </Drawer>
  </div>
</template>

<script>
import MonitoringInfoList from "@/components/monitoringInfoList";
import ListControls from "@/components/listControls/index.vue";
import platformIcon from "@/components/platformIcon";
import moment from "moment";
import platform from "@/assets/json/situations";
import timeSelect from "./components/times.vue"; //时间选择组件
import searchInput from "./components/searchInput.vue";
import sorts from "./components/sorts.vue";
import OriginalText from "@/components/originalText"; //预览原文
import StatisticalAnalysis from "./components/statisticalAnalysis.vue";
import RelatesInformation from "./components/relatesInformation.vue";
import informationProce from "./components/informationProce.vue";
import operationLog from "./components/operationLog.vue";
import InfoExchange from "./components/InfoExchange.vue";
// import postTrajectory from "./components/postTrajectory.vue";

const dayList = [
  { label: "全部", value: "-1" },
  { label: "今日", value: "0" },
  { label: "近三日", value: "2" },
  { label: "近一周", value: "6" },
  { label: "近一月", value: "30" },
  { label: "自定义", value: "-99" },
];
const JCCZ_SERVER="";
export default {
  name: "archiveDetail",
  data() {
    return {
      // 数据可以通过props传入
      dayList,
      dayNum: "-1",
      dateList: null,
      platform,
      btnStatus: {
        hidepoint: true,
        daBiao: true,
        handelTips: true,
      },
      tabSelect: "1",
      tableData: [],
      loading: true,
      total: 0,
      pageNo: 1,
      pageSize: 10,
      checkAll: false,
      checkAllGroup: [],
      indeterminate: false,
      countInfo: {},
      countBaseInfo: {},
      homePageUrl: '', // 存储主页链接
      timeList: [
        { id: 0, title: "全部" },
        { id: 6, title: "近一周" },
        { id: 30, title: "近一月" },
        // { id: -1, title: "自定义" },
      ], //时间列表
      timeId: 6,
      orderByTypeList: [
        {
          name: "发布时间",
          key: "1",
        },
        {
          name: "发布时间",
          key: "2",
        },
      ],
      timeArr: [],
      params: {
        orderByType: "1",
        keyword: "",
        searchPosition: "1",
      },
      componentId: "",
      drawerData: null,
      drawer: false,
      account: ["qianhongguo1", "chensu3", "chenzhe","","dingtong","weishujie1","bijianjun","chenguohua","xupengxiang"],
      currentAccount: localStorage.getItem("userAccount"),
      currentOrganId: localStorage.getItem("organId"),
        phoneTip: -1, //该账号有手机号(存在市办库里), 0:没有, 1:有 2:审核中
        hasUserId:-1,// 有没有用户id 0:没有 1:有 -1: 还在请求
    };
  },
  components: {
    MonitoringInfoList,
    ListControls,
    platformIcon,
    timeSelect,
    searchInput,
    OriginalText,
    sorts,
    StatisticalAnalysis,
    RelatesInformation,
    informationProce,
    operationLog,
    InfoExchange
  },
  mounted() {
    this.getList();
    this.getCount();
      this.phoneTip = this.account.includes(this.currentAccount) || this.currentOrganId == 244 ? 3 : 0;
    this.getLog("全息档案库/自媒体库", '查看账号详情/【'+this.$route.query.uname+'】关联信息页浏览');
    this.historyLog("全息档案库/自媒体库", '查看账号详情/'+this.$route.query.uname,this.$route.query.ukey);
  },
  methods: {
    dateChange(d) {
      this.dateList = d;
    },
    handleTime(val) {
      this.pageNo = 1;
      if (typeof val == "object") {
        this.timeId = val.id;
        this.timeArr = [val.startTime + " 00:00:00", val.endTime + " 23:59:59"];
      } else {
        this.timeId = val;
        this.timeArr = [];
      }
      this.getList();
    },
    pageSizeChange(d) {
      this.pageNo = 1;
      this.pageSize = d;
      this.getList();
    },
    pageNoChange(d) {
      this.pageNo = d;
      this.getList();
    },
    handleCheckAll() {
      if (this.indeterminate) {
        this.checkAll = false;
      } else {
        this.checkAll = !this.checkAll;
      }
      this.indeterminate = false;

      if (this.checkAll) {
        this.checkAllGroup = this.tableData.map((i) => i.mkey);
      } else {
        this.checkAllGroup = [];
      }
    },
    checkAllGroupChange(data) {
      if (data.length === this.tableData.length) {
        this.indeterminate = false;
        this.checkAll = true;
      } else if (data.length > 0) {
        this.indeterminate = true;
        this.checkAll = false;
      } else {
        this.indeterminate = false;
        this.checkAll = false;
      }
    },
    getList() {
      let userName = this.$route.query.uname;
      if(userName){
        userName = decodeURIComponent(userName);
      }
      this.checkAll = false;
      this.checkAllGroup = [];
      this.indeterminate = false;
      this.loading = true;
      this.tableData = [];
      let params = {
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        ukey: this.$route.query.ukey,
        situation: this.$route.query.situation,
        userName,
        ...this.params,
      };
      params.dayNum = this.dayNum;
      if(this.dayNum == '-99'){
        params.startTime = moment(this.dateList[0]).format("YYYY-MM-DD HH:mm:ss");
        params.endTime = moment(this.dateList[1]).format("YYYY-MM-DD HH:mm:ss");
      }
      this.$http
        .get("/accountInfoNew/accountMsgList", { params })
        .then((res) => {
          if (res.body.status === 0) {
            this.tableData = res.body.data.list || [];
            this.total = res.body.data.total || 0;
          } else {
            this.$Message.error("服务器错误！");
          }
          this.loading = false;
        });
    },
    getCount(cb) {
    this.$http
                .get(JCCZ_SERVER + "/accountApply/accountInfo", {
                    params: {
                        ukey: this.$route.query.ukey,
                        situation: this.$route.query.situation,
                        mkey: this.$route.query.mkey,
                    },
                })
                .then((res) => {
                   const situations = {
                        30: "媒体网站",
                        31: "客户端",
                        10: "新浪微博",
                        199: "短视频",
                        20: "微信公众号",
                        80: "小红书",
                        60: "论坛贴吧",
                        600: "今日头条",
                    };
                    if (this.$route.query.mkey) {
                        this.countBaseInfo.accountName = decodeURIComponent(
                            this.$route.query.uname
                        );
                      this.countBaseInfo.mediumName =
                            situations[this.$route.query.situation];
                        this.countBaseInfo.situation =
                            this.$route.query.situation;

                        if (res.body&&res.body.data&&res.body.data.data&&res.body.data.data.data&&res.body.data.data.data.userId) {


                            const { href } = this.$router.resolve({
                                path: "/main/archiveDetail",
                                query: {
                                    ukey: res.body.data.data.data.userId,
                                    situation: this.$route.query.situation,
                                },
                            });
                            window.location.replace(href);
                            return;
                        }

                        cb && cb();
                        return;
                    }

                    //todo zlt
                    let data = res.body.data;
                    if(!data){
                      data = {};
                      data.data = {};
                    }
                    this.countInfo = data.data.countInfo || {};
                    this.countBaseInfo = data.data.data || {};
                    this.homePageUrl = data.data.url || ''; // 获取主页链接
                    console.log("this.countBaseInfo",this.countBaseInfo);
                    if(!data.data.data){
                      this.hasUserId = 0;
                    }
                    if(data.data.data&&data.data.data.userId){
                      this.hasUserId = 1;
                    }else{
                      this.hasUserId = 0;
                    }
                    if(!this.countBaseInfo.accountName&&this.$route.query.uname){
                         this.countBaseInfo.accountName = decodeURIComponent(
                            this.$route.query.uname
                        );
                    }
                     this.countBaseInfo.mediumName =
                            situations[this.$route.query.situation];

                        if(!this.countBaseInfo.userId&&this.$route.query.ukey){
                         this.countBaseInfo.userId = this.$route.query.ukey;
                    }

                    if (
                        this.countBaseInfo.userId &&
                        this.countBaseInfo.userId.includes("addJcHand")
                    ) {
                        this.countBaseInfo.userId = "";
                    }
                    // this.phoneTip = data.data.phoneTip;
                    cb && cb();

                    // this.countBaseInfo.name = '';
                    // this.countBaseInfo.sex = '';
                    // this.countBaseInfo.idCard = '';
                    // this.countBaseInfo.date = '1990-01-01';
                    // this.countBaseInfo.address = '5';
                    // this.countBaseInfo.phone = '';
                    // this.countBaseInfo.email = '';
                    // this.phoneTip = 3;
                });
    },
     openXxwsModal() {
            const xxws = () => import("../../accountDetails/components/xxws.vue");
            const that = this;
            this.$modal.show({
                component: xxws,
                componentProps: { data: this.countBaseInfo, that },
                componentEvents: {
                    close: (d) => {


                        if (d !== "cancel") {

                            if(d.userId){
                            const { href } = this.$router.resolve({
                                path: "/main/archiveDetail",
                                query: {
                                    ukey: d.userId,
                                    situation: that.$route.query.situation,
                                },
                            });
                            window.location.replace(href);
                            }else{
                                this.getCount();
                            }
                        }
                        that.$modal.hide();
                    },
                },
                title: "信息完善", // 传递标题
                zIndexWrap: 10,
                // y: 300,
            });
        },
        openAddxxModal() {
            const Addxx = () => import("../../accountDetails/components/addxx.vue");
            const that = this;
            this.$modal.show({
                component: Addxx,
                componentProps: {
                    data: this.countBaseInfo,
                    that,
                    route: {
                        mkey: this.$route.query.mkey,
                        situation: this.$route.query.situation,
                    },
                },
                componentEvents: {
                    close: (d) => {
                        console.log("d", d);

                        if (d != "cancel") {
                            const { href } = this.$router.resolve({
                                path: "/main/archiveDetail",
                                query: {
                                    ukey: d,
                                    situation: that.$route.query.situation,
                                },
                            });
                            window.location.replace(href);
                        }
                        that.$modal.hide();
                    },
                },
                title: "账号添加", // 传递标题
                zIndexWrap: 10,
                // y: 300,
            });
        },
    //导出
    derive() {
      
      // if (this.checkAllGroup.length === 0) {
      //   return this.$Message.warning("请选择信息后重试！");
      // }
      //直接全部导出
      let userName = this.$route.query.uname;
      if(userName){
        userName = decodeURIComponent(userName);
      }
      let params = {
        ukey: this.$route.query.ukey,
        situation: this.$route.query.situation,
        userName,
        ...this.params,
        mkey:"202503y2A5527CA416448CFAB4B8AF1E68193DF"
      };
      params.dayNum = this.dayNum;
      if(this.dayNum == '-99'){
        params.startTime = moment(this.dateList[0]).format("YYYY-MM-DD HH:mm:ss");
        params.endTime = moment(this.dateList[1]).format("YYYY-MM-DD HH:mm:ss");
      }
      this.$http
        .get("/accountInfoNew/exportAccountMsgList", {params,responseType: "blob"})
        .then((res) => {
          const disposition = res.headers.get("Content-Disposition");
          let filename = "downloaded_file";
          if (disposition && disposition.indexOf("attachment") !== -1) {
            const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
            const matches = filenameRegex.exec(disposition);
            if (matches != null && matches[1]) {
              filename = matches[1].replace(/['"]/g, "");
              filename = decodeURIComponent(escape(filename)); // 解码文件名
            }
          }
          const blob = new Blob([res.body]);
          const a = document.createElement("a");
          a.href = URL.createObjectURL(blob);
          a.download = filename;
          document.body.appendChild(a);
          a.click();

          setTimeout(() => {
            document.body.removeChild(a);
            URL.revokeObjectURL(a.href);
          }, 0);
        });
    },
    handleSearch() {
      this.pageNo = 1;
      this.getList();
    },
    openDrawer(d, type) {
      this.drawerData = d;
      this.componentId = "OriginalText";
      this.drawer = true;
    },
    changeSort(item) {
      this.params.orderByType = item.key;
      this.handleSearch();
    },

    // 生成二维码URL
    generateQRCode(url) {
      if (!url) return '';
      // 使用在线二维码生成服务，您也可以替换为其他服务
      return `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(url)}`;
    }
  },
  computed: {
    moment() {
      return moment;
    },
    handleSource() {
      return (
        this.countBaseInfo.mwebsiteName ||
        this.platform[this.countBaseInfo.situation] ||
        ""
      );
    },
    userIdLength() {
      return (this.countBaseInfo.userId || "").length;
    },
    fullUserId() {
      return this.countBaseInfo.userId || "";
    },
    displayedUserId() {
      // if (this.userIdLength > 10) {
      //   return this.countBaseInfo.userId.substring(0, 10) + "...";
      // }
      return this.countBaseInfo.userId || "-";
    },
  },
  watch: {
    dayNum: {
      handler(newVal) {
        if(newVal != '-99'){
          this.pageNo = 1;
          this.getList()
        }
      },
    },
    dateList: {
      handler(newVal) {
        if(newVal && newVal.length > 0){
          this.pageNo = 1;
          this.getList()
        }
      },
    },

  },
};
</script>

<style lang="less" scoped>
.account-info-container {
  padding: 10px;
  padding-right: 30px;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}
.account-header {
  display: flex;
  background-color: #fff;
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
  padding: 30px;
  overflow: hidden;
  flex-shrink: 0;
 .left {
        
        flex: 1.5;
        overflow: hidden;
        display: flex;
        .account-baseinfo {
            width: 80px;
            text-align: center;
            font-size: 0;
            .avatar {
                width: 100px;
                height: 100px;
                overflow: hidden;
                border-radius: 50%;
            }
            .platform-name {
                margin-top: 10px;
                font-size: 16px;
                font-weight: bold;
                color:#6D9DED
            }
        }
        .account-info {
            margin-left: 30px;
            flex: 1;
            overflow: hidden;
            .account-title {
                font-size: 14px;
                font-weight: bold;
                margin-bottom: 30px;
                align-items: center;
                .account-title-left{
                  display: flex;
                  align-items: center;
                 
                }
                .btn-action {
                    margin-left: 20px;
                    cursor: pointer;
                    padding: 4px 12px;
                    border-radius: 4px;
                    font-size: 14px;
                    color: #333;

                    &.btn-blue {
                        background: #e6f4fe;
                        border: 1px solid #1890ff;
                    }
                    &.btn-orange {
                        background: #fff6eb;
                        border: 1px solid #ff951c;
                    }
                    &.btn-green {
                        background: #e6f7ff;
                        border: 1px solid #1dbfac;
                        cursor: default;
                        color: #1dbfac;
                    }
                }
            }
            .account-info-box {
                display: flex;
                
                justify-content: space-between;
                &.text{
                  color: #D9001B;
                  font-size: 14px;
                  padding-right: 0;
                  justify-content: start;
                  align-items: center;
                  span{
                    margin-left: 10px;
                  }
                }

                .account_div {
                    flex:1;
                    flex-shrink: 0;
                    display: flex;
                    // border: 1px solid rgba(129, 211, 248, 0.2);
                    .lable {
                        display: inline-block;
                        width: 30px;
                        text-align: center;
                        //圆渐变色 
                        background: linear-gradient(to bottom , #A2A4E2, #99BDEF,#A2A4E2);
                        border-radius: 5px 0 0 5px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        font-size: 14px;
                        writing-mode: vertical-rl;
                        color:#fff;
                        font-weight: 600;
                    }
                    &>.lable{
                      position: relative;
                      &::before{
                        content:"";
                        display: block;
                        width: 3px;
                        height: 50px;
                        //渐变
                        background: linear-gradient( rgba(255,255,255,0) 0%,rgba(255,255,255,1) 83%,rgba(255,255,255,0) 83.1%,rgba(255,255,255,0) 90%,rgba(255,255,255,1) 90.1%,rgba(255,255,255,1) 100% );
                 
                        position: absolute;
                        left: 50%;
                        top: 5px;
                        transform: translateX(-50%);
                      }
                      &::after{
                        content:"";
                        display: block;
                        width: 3px;
                        height: 50px;
                        //渐变
                        background: linear-gradient( rgba(255,255,255,0) 0%,rgba(255,255,255,1) 83%,rgba(255,255,255,0) 83.1%,rgba(255,255,255,0) 90%,rgba(255,255,255,1) 90.1%,rgba(255,255,255,1) 100% );
                        //旋转
                        
                        position: absolute;
                        left: 50%;
                        bottom: 5px;
                        transform: translateX(-50%) rotate(180deg);
                      }
                    }
                    .info-item {
                        margin-bottom: 18px !important;
                        display: flex;
                        &:last-child{
                          margin-bottom: 5px !important;
                        }
                        .label{
                          color:#a8a8a8;
                          
                        }
                        .value{
                          flex:1;
                          // color:red
                        }
                    }
                }
                .user_div {
                    margin-left: 10px;
                    flex:1.5;
                    flex-shrink: 0;
                    display: flex;
                    // border: 1px solid rgba(202, 249, 130, 0.2);
                    .lable {
                        display: inline-block;
                        width: 30px;
                        text-align: center;
                        background: linear-gradient(to bottom, #A2A4E2, #99BDEF, #A2A4E2);
                        border-radius: 5px 0 0 5px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        font-size: 14px;
                        writing-mode: vertical-rl;
                        line-height: 16px;
                        color:white;
                        font-weight: 600;
                    }
                    &>.lable{
                      position: relative;
                      &::before{
                        content:"";
                        display: block;
                        width: 3px;
                        height: 50px;
                        //渐变
                        background: linear-gradient( rgba(255,255,255,0) 0%,rgba(255,255,255,1) 83%,rgba(255,255,255,0) 83.1%,rgba(255,255,255,0) 90%,rgba(255,255,255,1) 90.1%,rgba(255,255,255,1) 100% );
                 
                        position: absolute;
                        left: 50%;
                        top: 5px;
                        transform: translateX(-50%);
                      }
                      &::after{
                        content:"";
                        display: block;
                        width: 3px;
                        height: 50px;
                        //渐变
                        background: linear-gradient( rgba(255,255,255,0) 0%,rgba(255,255,255,1) 83%,rgba(255,255,255,0) 83.1%,rgba(255,255,255,0) 90%,rgba(255,255,255,1) 90.1%,rgba(255,255,255,1) 100% );
                        //旋转
                        
                        position: absolute;
                        left: 50%;
                        bottom: 5px;
                        transform: translateX(-50%) rotate(180deg);
                      }
                    }
                    .value{
                      // color:red;
                      flex:1;
                    }
                    .info-list {
                      flex-wrap:wrap;
                      flex-direction: row;
                      display: flex;
                      .info-item{
                      max-width: 50%;
                      width: 50%;
                      display: flex;
                      flex-direction: row;
                    }
                    }
                    .info-list .info-item .ellipsis{
                      // max-width: 138px;
                    }
                }
               
                .info-list {
                    padding: 14px;
                    flex-shrink: 0;
                    font-size: 14px;
                    overflow: auto;
                    max-height: 204px;
                    flex: 1;
                    //渐变色
                    background: linear-gradient(to right, white, #E5EFFD);
                    border-radius:0  10px 10px 0;
                    display: flex;
                    flex-direction: column;
                   
                    &.user_tip_box {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        .user_tip {
                            display: flex;
                            align-items: center;

                            span {
                                margin-left: 5px;
                                color: #70b603;
                            }
                        }
                    }

                    .info-item {
                        margin-bottom: 13px;
                        line-height: 16px;
                        // display: block;
                      
                        .label-icon {
                            font-size: 12px;
                            line-height: 16px;
                            vertical-align: middle;
                        }
                        .label{
                          color:#a8a8a8
                        }
                        .ellipsis {
                            // max-width: 170px;
                            display: inline-block;
                            vertical-align: middle;
                        }
                        &:last-child {
                            margin-bottom: 5px;
                        }
                    }

                }
            }
        }
    }
  .right {
    flex: 1;
    overflow: hidden;
    padding-left: 20px;
    display: flex;
    row-gap: 20px;
    .stat-header {
      font-size: 14px;

    }
    .icon {
      font-size: 28px;
    }

    .stat-content {
      display: flex;
      justify-content: space-between;
      align-items: flex-end;
      height: 28px;
  
      .number {
        font-size: 20px;
        line-height: 1em;
        font-weight: bold;
      }
      .sub-info {
        font-size: 14px;
        position: absolute;
        right: 17px;
        bottom: 10px;
        .percentage {
          display: inline-block;
          line-height: 1;
          padding: 2px 4px;
          // background-color: #90c2f9;
          border-radius: 3px;
        }
      }
    }
    .stat-card {
      border-radius: 5px;
      box-sizing: border-box;
      color: #fff;
      padding: 10px;
    }
    .stats-left {
      flex: 3;
      flex-shrink: 0;
      overflow: hidden;
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      .stat-card {
        width: calc(~"(100% - 40px )/3");
        flex-direction: column;
        align-items: start;
        display: flex;
        justify-content: center;
        padding-left:62px;
        position: relative;
        .icon{
          position: absolute;
          left: 12px;
          top: 50%;
          transform: translateY(-50%);
        }
        .bg{
          position: absolute;
          right:  -12px;
          bottom:0;
  
          opacity: 0.2;
        }
        .stat-header, .stat-content{
          font-size: 18px;
          font-weight: 600;
          display: block;
          width: 100%;
          align-items: start;
          justify-content: start;
        }

      }
    }
    .stats-right {
      flex: 1;
      flex-shrink: 0;
      overflow: hidden;
      .stat-card {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        .stat-header {
          font-weight: bold;
          font-size: 16px;
          
          .icon {
            color: #fff;
            font-size: 16px;
            display: inline-block;
            cursor: pointer;
          }
        }
        .stat-content {
          margin-top: 10px;
          .number {
            font-size: 56px;
          }
        }
      }
    }
  }
}

.icon {
  font-size: 20px;
}
.purple {
  background: linear-gradient(to right, #c382ff, #d8c2f7);
}
.blue {
  background: linear-gradient(to right, #4b96f3, #a8d7f6);
}

.green {
  background: linear-gradient(to right, #0bc5bb, #aef1e6);
}

.orange {
  background: linear-gradient(to right, #f98053, #ffdbc0);
}

.pink {
  background: linear-gradient(to right, #ed99a3, #efe3e3);
}

.coffee {
  background: linear-gradient(to right, #6f0412, #ebc9cd);
}

.color_qing{
  background: linear-gradient( 135deg, #9BEAFF 0%, #4AB9E9 100%);
}
.color_qunqing{
  background: linear-gradient( 135deg, #9BAEFF 0%, #4A7FE9 100%);
}
.color_cheng{
  background: linear-gradient( 135deg, #FFB79B 0%, #E9624A 100%);
}
.color_blue{
  background: linear-gradient( 135deg, #9BDBFF 0%, #4AA8E9 100%);
}
.color_ju{
  background: linear-gradient( 135deg, #FF9B9B 0%, #E94A4A 100%);
}
.color_green{
  background: linear-gradient( 135deg, #94EFC3 0%, #53CB86 100%);
}

.content-wrapper {
  flex: 1;
  overflow: hidden;
  .operation_right {
    display: flex;
    gap: 20px;
    justify-content: start;
    align-items: start;
  }
  .select {
    background: #fff;
    border-radius: 8px;
    font-family: PingFang SC;
    font-size: 14px;
    padding: 10px 20px;
    margin-bottom: 10px;
  }
}
.content_box {
  height: 100%;
  // height: calc(~"100% - 80px");
  background-color: #fff;
  padding: 20px 40px 20px 20px;
  border-radius: 8px;
  .header {
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .btn {
      background: #5585ec;
      border-radius: 4px;

      padding: 0 18px;
      color: #fff;
      margin-right: 20px;
      cursor: pointer;
    }

    .operation {
      font-size: 16px;
      display: flex;
      align-items: center;

      .btn {
        line-height: 30px;
        height: 30px;
      }

      /deep/ .ivu-checkbox-wrapper {
        font-size: 16px;
        margin-right: 50px;
        line-height: 40px;
      }

      /deep/ .ivu-checkbox-inner {
        width: 16px;
        height: 16px;
      }
    }

    .playBack {
      line-height: 40px;
      height: 40px;
    }
  }

  .list {
    height: calc(~"100% - 150px");
    overflow-y: auto;
    // padding-bottom: 20px;
    position: relative;
  }
}
.tooltip-content {
  width: 360px;
  white-space: normal;
  .bluetext {
    color: #70bcdf;
  }
  p {
    font-size: 12px;
    line-height: 2;
    white-space: normal;
  }
}
/deep/.ivu-tabs-nav-container{
  height:50px;
}
/deep/.ivu-tabs-bar{
 border-bottom: none;
 margin-bottom: 10px;
}
/deep/.ivu-tabs-nav-scroll {
  font-weight: 600;
  font-size: 20px;
  border-bottom: none;
}
/deep/.ivu-tabs-nav{
  display: flex;
  flex-direction: row;
  padding-bottom:10px;
}
/deep/.ivu-tabs-ink-bar{
  display: none;
}
/deep/ .ivu-tabs-nav .ivu-tabs-tab{
    width: 160px;
    height: 40px;
    background: #ebedf8;
    border-radius: 4px;
    box-shadow: 0 3px 6px rgba(80, 80, 80, 0.22);
    margin-right: 18px;
    line-height: 40px;
    text-align: center;
    color: #666;
    font-size: 16px;
    position: relative;
    cursor: pointer;
    padding:0;
    transition:0s;
  
    &.ivu-tabs-tab-active{
      background: #4F88E9;
      color:white;
      &:after{
        content: '11';
        position: absolute;
        display: block;
        left: 50%;
        transform: translateX(-50%);
        bottom: -8px;
        width: 0;
        height: 0;
        border-left: 9px solid transparent;
        border-right: 9px solid transparent;
        border-top: 8px solid #5585ec;
       
      }
    }
}

/deep/.sort-content .select{
  border:1px solid #5585ec;
  color:#5585ec;
  margin-right: 0px;
  height:30px;
  line-height: 30px;
}
/deep/.sort-content .select .xian{
  height:100%;
}
/deep/.ivu-select-single .ivu-select-selection{
  height:28px;
}
/deep/.ivu-input-wrapper{
  top:-0px
}
/deep/.ivu-input-group-append, /deep/.ivu-input-group-prepend{
  border-color:#5585ec;
  background: #E6F7FF;
  color:#6C6D6D
}
/deep/.ivu-select-single .ivu-select-selection .ivu-select-selected-value{
  line-height: 28px;
  color:#5585ec
}
/deep/.ivu-input{
  border-color:#5585ec;
  height:30px;

  //placeholder 颜色

  /deep/&::placeholder {
    color:#6C6D6D
  }
}
/deep/.ivu-icon-ios-search{
  color:#5585ec;
  width:30px;
  height:30px;
  font-size: 20px;
  top:0;
}
.account-name {
    display: inline-block;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    line-height: 22px;
    color:#858585
}
.MonitoringInfoList {
    /deep/ .title.ellipsis,
    /deep/ .abstract.ellipsis {
        white-space: normal;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    // 二维码图标样式
    .qr-code-icon {
        display: inline-flex;
        align-items: center;
        vertical-align: middle;

        .svg-icon {
            transition: color 0.2s;

            &:hover {
                color: #3f7ee8 !important;
            }
        }
    }

    // 二维码悬浮提示样式
    .qr-code-tooltip {
        text-align: center;
        padding: 8px;
        background: white;
        border-radius: 4px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

        img {
            border-radius: 4px;
        }

        p {
            margin: 8px 0 0 0;
            font-size: 12px;
            color: #666;
            word-break: break-all;
            max-width: 200px;
        }
    }
    /deep/ .ellipsis {
        white-space: unset;
        text-overflow: unset;
        overflow: unset;
    }
}
</style>
