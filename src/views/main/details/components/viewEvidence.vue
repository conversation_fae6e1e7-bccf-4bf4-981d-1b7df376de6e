<template>
    <!-- 账号添加 -->
    <div class="index addnewAccount">
        <div class="content">
            <div class="box" style="width:350px">
                <div class="titbox" >
                    <div class="line"></div>
                    <div>取证记录</div>
                </div>
                <Steps :current="1" direction="vertical">
                    <Step title="已完成" content="这里是该步骤的描述信息"></Step>
                    <Step title="进行中" content="这里是该步骤的描述信息"></Step>
                    <Step title="待进行" content="这里是该步骤的描述信息"></Step>
                    <Step title="待进行" content="这里是该步骤的描述信息"></Step>
                </Steps>
            </div>
            <div class="box" style="width:350px">
                <div class="titbox" >
                    <div class="line"></div>
                    <div>基本信息</div>
                </div>
                <div class="txtcon">
                    <span>
                        <span class="tit">网页标题：</span>
                    </span>
                    <span>
                        <span class="tit">取证地址：</span>
                    </span>
                    <span>
                        <span class="tit">取证时间：</span>
                    </span>
                    <span>
                        <span class="tit">取证人：</span>
                    </span>
                </div>
            </div>
            <div class="box" style="width:700px">
                <div class="titbox" >
                    <div class="line"></div>
                    <div>取证截图</div>
                </div>
                <img src="https://file.iviewui.com/images/image-demo-13.jpg" alt="" style="width:100%">
                    <!-- <Image src="https://file.iviewui.com/images/image-demo-13.jpg" /> -->
            </div>
        </div>
    </div>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具 js，第三方插件 js，json 文件，图片文件等等）
// 例如：import  《组件名称》  from '《组件路径》 ';

const situations = {
    30: "媒体网站",
    31: "客户端",
    10: "新浪微博",
    199: "短视频",
    20: "微信公众号",
    80: "小红书",
    60: "论坛贴吧",
    600: "今日头条",
};
export default {
    data() {
        // 这里存放数据
        return {
        };
    },
    // import 引入的组件需要注入到对象中才能使用
    components: {},
    props: {
        data: {
            type: Object,
            default: () => ({}),
        },
        that: {
            type: Object,
            default: null,
        },
    },
    // 方法集合
    methods: {
    },
    // 监控 data 中的数据变化
    watch: {
    },
    //过滤器
    filters: {},
    // 生命周期 - 挂载完成（可以访问 DOM 元素）
    mounted() {},
};
</script>

<style scoped lang="less">
.index {
    width: 1500px;
    border-radius: 8px;
    overflow: hidden;

    .content {
        display: flex;
        justify-content: space-between;
        background: white;
        padding: 0px 10px;
        position: relative;
        margin-bottom: 30px;
        .line {
            margin-right: 10px;
            width: 6px;
            height: 15px;
            background-color: rgb(85,133,255) ;
        }
        .box {
            padding: 20px 10px;
            border-radius: 4px;
            height: 650px;
            box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.16);
            .txtcon {
                display: flex;
                flex-direction: column;
                .tit {
                    font-size: 16px;
                    color: #999;
                }
            }
        }
        .titbox {
            display: flex;
            align-items: center;
            font-size: 24px;
            color: #666;
            font-weight: 600;
        }
    }

    // 添加加载状态样式
    .loading-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 200px;

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #4a90e2;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 15px;
        }

        .loading-text {
            color: #666;
            font-size: 16px;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }
    }
    .form_group {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .form {
            display: flex;
            width: 49%;
            &.left {
                border: 2px solid rgba(129, 211, 248, 0.2);
                .form_name {
                    background-color: rgba(129, 211, 248, 0.2);
                }
            }
            &.right {
                border: 2px solid rgba(202, 249, 130, 0.2);
                .form_name {
                    background-color: rgba(202, 249, 130, 0.2);
                }
            }
            .form_name {
                font-size: 16px;
                color: #333;
                writing-mode: vertical-rl;
                display: flex;
                align-items: center;
                justify-content: center;
                letter-spacing: 4 px;
                // font-weight: bold;
                // vertical-align: middle;
            }
            .form_box {
                padding: 10px;
                flex: 1;
                max-height: 490px;
                overflow-y: auto;
            }
        }
    }

    .form-item {
        display: flex;
        margin-bottom: 20px;
        font-size: 16px;
        color: #333;
        align-items: center;

        .label {
            width: 113px;
            text-align: right;
            margin-right: 10px;
        }

        .value {
            flex: 1;

            &.highlight {
                color: #4a90e2;
                font-weight: bold;
            }

            &.value_phone {
                display: flex;
                align-items: center;
                white-space: nowrap;
            }
            /deep/ .ivu-input {
                font-size: 16px;
                color: #333;
            }
            .required_value {
                display: flex;
                align-items: center;
                flex: 1;
            }
            .required {
                padding-left: 10px;
                color: red;
            }
        }

        .phone-validate {
            color: red;
            margin-left: 10px;
            font-size: 14px;
        }
    }

    .form-note {
        color: red;
        font-size: 14px;
        margin: 50px 0 20px 0px;

        text-align: center;
    }

    .footer {
        display: flex;
        justify-content: center;
        gap: 20px;

        button {
            width: 120px;
            height: 40px;
            border-radius: 4px;
            border: none;
            font-size: 16px;
            cursor: pointer;
            color: white;
            transition: background-color 0.3s;

            &.submit-btn {
                background-color: #4a90e2;
                &:disabled {
                    background-color: #a0c3ee;
                    cursor: not-allowed;
                }
            }

            &.cancel-btn {
                background-color: #9e9e9e;
                &:disabled {
                    background-color: #cccccc;
                    cursor: not-allowed;
                }
            }
        }
    }
      .form.left{
    .form-item{
        margin-bottom: 35px;
    }
}
}
/deep/ .ivu-radio-wrapper{
    font-size: 16px;
}
</style>
