<template>
  <div class="details">
    <div class="box flex sb">
      <!-- 左侧信息详情 -->
      <div class="InfoDetails">
        <div class="top flex sb">
          <div class="title">
            阅读人数：<span style="color: #5585ec;">{{ uv }}</span>
          </div>
          <ListControls
            v-if="token && !isQx"
            :btnStatus="btnStatus"
            :data="details"
            :delMsg="delMsg"
            @editAbstract="editAbstract"
            @setProvincOfficeTips="setProvincOfficeTips"
          />
        </div>
        <!-- 中间内容区域 -->
        <components
          :is="componentId"
          :data="details"
          class="content"
          v-if="details && details.mkey"
          @editTitle="editMsgtitle"
        />
        <Spin fix v-else>
          <Icon type="ios-loading" size="18" class="demo-spin-icon-load"></Icon>
          <div>Loading</div>
        </Spin>

        <!-- 评论转发 -->
        <Carousel
          v-if="!details.videoFile && details.pictureFiles"
          v-model="CarouselId"
          :arrow="
            details.pictureFiles.split(';').length > 1 ? 'always' : 'never'
          "
          dots="outside"
          :key="CarouselKey"
          @on-change="CarouselChange"
        >
          <CarouselItem
            v-for="(item, index) in details.pictureFiles.split(';')"
            :key="index"
          >
            <div class="demo-carousel">
              <!-- <img :src="item" alt="" @click="blowUp" /> -->
              <img :src="imageBaseUrl + item" alt="" @click="blowUp" />
            </div>
          </CarouselItem>
        </Carousel>
      </div>
      <!-- 右侧附带信息 -->
      <div class="RelevantInfo">
        <div class="tabBox flex sb">
          <div
            v-for="(v, k) in tabList"
            :key="k"
            :class="['tab', 'cp', tabId === k ? 'active' : '']"
            @click="tabId = k"
          >
            {{ v }}
          </div>
        </div>

        <components
          :is="tabId === '1' ? 'StudyJudge' : tabId === '2' ? 'Source' : 'Logs'"
          :key="itemKey"
          :data="details"
          class="content"
        />
      </div>
    </div>
    <!-- :imgList="details.pictureFiles.split(';').map((i) => imageBaseUrl + i)" -->

    <ImgPreview
      v-if="ImgPreviewStatus"
      :defaultId="CarouselId"
      :imgList="details.pictureFiles.split(';').map((i) => imageBaseUrl + i)"
      @close="ImgPreviewStatus = false"
    />
    <Drawer
      title=""
      class="drawer_content"
      :closable="false"
      v-model="drawerVisi"
      width="1300"
    >
      <div class="edit_content" @click="drawerVisi = false" v-if="drawerVisi">
        <Badge :count="details.isTobeEditNumber || 0">
          <Icon type="ios-create-outline" />
        </Badge>
        批量编辑
      </div>
      <ContentEditor
        v-if="drawerVisi"
        :detailData="details"
        @save="saveDetailEdit"
      ></ContentEditor>
    </Drawer>
    <div class="edit_content" @click="drawerVisi = true">
      <Badge :count="details.isTobeEditNumber || 0">
        <Icon type="ios-create-outline" />
      </Badge>
      批量编辑
    </div>
    <div v-if="isFirstInit">
      <ContentEditor
        v-show="false"
        :justView="true"
        :detailData="details"
        @save="() => {}"
        :changeNumber="changeNumber"
      ></ContentEditor>
    </div>
  </div>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具 js，第三方插件 js，json 文件，图片文件等等）
// 例如：import  《组件名称》  from '《组件路径》 ';
// import InfoDetails from "@/views/main/details/components/InfoDetails.vue";
// import AccountDetails from "./components/AccountDetails.vue";
import NewsDetails from "./components/newsDetails";
import WeiboDetails from "./components/weiboDetails";
import VideoDetails from "./components/videoDetails";

import CommentForward from "./components/CommentForward";

import ListControls from "@/components/listControls/index.vue";
import StudyJudge from "./components/RelevantInfo/StudyJudge.vue";
import Source from "./components/RelevantInfo/Source";
import Logs from "./components/RelevantInfo/Logs";

import ImgPreview from "@/components/imgPreview";
import ContentEditor from "./components/ContentEditor.vue";

const pathList = {
  "/main/comprehensiveSearch/searchResults": {
    moduleType: "1",
    moduleSecondType: "2",
  }, //搜索结果
  "/main/shandongMonitor/recommended": { type: 2 }, //涉鲁推荐
  "/main/shandongMonitor/politicalRecommended": { type: 3 }, //涉政推荐
  "/main/monitor/recommended": {
    moduleType: "1",
    moduleSecondType: "1",
    type: 1,
  }, //涉济信息 涉济推荐模块
  "/main/monitor/message": { moduleType: "1", moduleSecondType: "2" }, //涉济信息 信息监测模块
  "/main/monitor/shortVideo": { moduleType: "1", moduleSecondType: "3" }, //涉济信息 短视频监测模块
  "/main/monitor/jiNanHotList": { moduleType: "1", moduleSecondType: "4" }, //涉济信息 涉济热榜模块
  "/main/monitor/chinaHotList": { moduleType: "1", moduleSecondType: "5" }, //涉济信息 国内热榜模块
  "/main/monitor/jiNansubmission": { moduleType: "1", moduleSecondType: "6" }, //涉济信息 涉济报送模块
  "/main/subjectDataBase/evidenceDataBase": { moduleType: "1", moduleSecondType: "7" }, //涉济信息 取证信息库模块
  // "": { moduleType: "", moduleSecondType: "" },
  // "": { moduleType: "", moduleSecondType: "" },
};

export default {
  data() {
    // 这里存放数据
    return {
      pathList,
      accountName: localStorage.getItem("userAccount"),
      details: {},
      btnStatus: {},
      componentId: null,
      tabList: {
        1: "研判",
        2: "溯源",
        3: "日志",
      },
      tabId: "1",

      obj: {
        NewsDetails: [30, 31, 20, 61, 62, 170, 41, 51, 80, 230],
        WeiboDetails: [10],
        VideoDetails: [48, 50, 110, 188, 199, 200],
      },
      itemKey: 0,
      loading: false,
      ImgPreviewStatus: false,
      CarouselId: 0,
      CarouselKey: 0,
      token: localStorage.getItem("tokens"),
      drawerVisi: false,
      isFirstInit: false,
      uv: 0,
    };
  },
  // import 引入的组件需要注入到对象中才能使用
  components: {
    // InfoDetails,
    // AccountDetails,
    NewsDetails,
    WeiboDetails,
    VideoDetails,
    CommentForward,
    ListControls,
    StudyJudge,
    Source,
    Logs,
    ImgPreview,
    ContentEditor,
  },
  props: {},
  // 生命周期 - 创建完成（可以访问当前this 实例）
  created() {
    console.log("ssxxx:" + this.token);
    this.getDetails();
  },
  // 方法集合
  methods: {
    openAddAccountModal() {
      const LookEvidence = () => import("@/components/LookEvidence");
      let params = {msgKey: this.$route.query.msgKey}
      this.$modal.show({
        component: LookEvidence,
        componentProps: {
          data: params,
          that: this,
          pathList: this.pathList,
          sourcePath: this.$route.path,
        },
        componentEvents: {
          // closes: this.tipCancelModel,
          blowUp: this.blowUp,
        },
        title: "查看取证", // 传递标题
        // y: 300,
      });
//       const Addxx = () => import("./components/viewEvidence.vue");
//       const that = this;
//       this.$modal.show({
//         component: Addxx,
//         componentProps: {
//           data: {},
//           that,
//         },
//         componentEvents: {
//           close: (d) => {
//             if (d != "cancel") {
//               this.getCount();
//               this.getList();
//             }
//             that.$modal.hide();
//           },
//         },
//         title: "查看取证", // 传递标题
//         zIndexWrap: 10,
//         // y: 300,
//       });
    },
    editMsgtitle() {
      const editAbstract = () => import("@/components/editMsgtitle");
      this.$modal.show({
        component: editAbstract,
        componentProps: { data: this.details, that: this },
        componentEvents: { close: this.cancelModel },
        title: "编辑标题", // 传递标题
        // y: 300,
      });
    },
    cancelModel() {
      this.$modal.hide();
    },
    //图片放大
    blowUp() {
      this.ImgPreviewStatus = true;
    },
    editAbstract(key, data) {
      console.log(key, data);
    },
    // 删除
    delMsg(d) {
      let params;

      if (d) {
        params = {
          ids: d.id,
        };
      } else {
        if (this.checkAllGroup.length === 0) {
          return this.$Message.warning("请选择信息后重试！");
        } else {
          params = {
            ids: this.checkAllGroup.toString(),
          };
        }
      }
      this.$http
        .post("/recommend/delMsg", params, { emulateJSON: true })
        .then((res) => {
          console.log(res);
          if (res.body.status === 0) {
            this.$Message.success(res.body.message);
            this.getCount();
          }
        });
    },
    //获取信息详情
    getDetails() {
      if (!this.$route.query.msgKey || (!this.$route.query.situation && !this.$route.query.showpop)) {
        this.$Message.warning("未找到对应信息。");
        return;
      }
      let params = {
        msgKey: this.$route.query.msgKey,
        situation: this.$route.query.situation,
      };
      let url = "/search/msgDetail";
      if (this.$route.query.sourceName === "境外监测") {
        url = "/abroad/msgDetail";
      }
      this.$http.get(url, { params }).then((res) => {
        console.log(res);
        if (res.body.status === 0) {
          this.loading = true;
          this.details = res.body.data ? res.body.data : {};
          this.isFirstInit = true;
          // if(this.details.isTobeEdit && this.details.isTobeEdit > 0){
          //   this.drawerVisi = false

          // }
          if (this.$route.query.moduleOriginName) {
            this.getLog(
              decodeURIComponent(this.$route.query.moduleOriginName),
              "正文页浏览/" + this.details.mtitle,
              this.$route.query.msgKey ? this.$route.query.msgKey : ""
            ).then((res) => {
              if (res.body && res.body.data) {
                this.uv = res.body.data;
              }
            });
          }
          this.itemKey++;
          document.title =
            "详情页-" + this.details && this.details.mtitle
              ? this.details.mtitle
              : "";
          for (const key in this.obj) {
            if (this.obj[key].includes(this.details.situation)) {
              this.componentId = key;
              break;
            } else {
              this.componentId = "NewsDetails";
            }
          }
          this.getTipStatus()
          this.addHistoryLog(this.details);
        } else {
          this.$Message.error(res.body.message);
          // this.details = require("./mock/index.js").default;
        }
      });
      // this.getLog(
      //   "综合搜索",
      //   "信息浏览/" + this.$route.query.msgKey,
      //   this.$route.query.msgKey
      // );
    },
    addHistoryLog(detail){
      let module = null;
      if (this.$route.query.sourcePath === "/main/monitor/recommended") {
        module = "涉济监测/涉济推荐";
      }else if (this.$route.query.sourcePath === "/main/monitor/message") {
        module = "涉济监测/专题监测";
      }else if (this.$route.query.sourcePath === "/main/monitor/shortVideo") {
        module = "涉济监测/短视频监测";
      }else if (this.$route.query.sourcePath === "/main/monitor/chinaImportanceNews") {
        module = "涉济监测/国内要闻监测";
      }else if (this.$route.query.sourcePath === "/main/monitor/overseasMonitor") {
        module = "涉济监测/境外监测";
      }else if (this.$route.query.sourcePath === "/main/comprehensiveSearch/searchResults") {
        module = "精确搜索";
      }else if (this.$route.query.sourcePath === "/main/eventAnalysis/eventInfo") {
        module = "事件分析/事件分析页";
      }else if (this.$route.query.sourcePath === "/main/OffshoreMonitoring/recommended") {
        module = "境外监测/涉济推荐";
      }else if (this.$route.query.sourcePath === "/main/OffshoreMonitoring/message") {
        module = "境外监测/专题监测";
      }else if (this.$route.query.sourcePath === "/main/OffshoreMonitoring/AccountMessage") {
        module = "境外监测/账号监测";
      }else if (this.$route.query.sourcePath === "/main/OffshoreMonitoring/websiteMessage") {
        module = "境外监测/网站监测";
      }else if (this.$route.query.sourcePath === "/main/OffshoreMonitoring/overseasMonitor") {
        module = "境外监测/境外搜索";
      }else if (this.$route.query.sourcePath === "/main/subjectDataBase/deleteMsgDataBase") {
        module = "专题数据/信息处置库";
      }else if (this.$route.query.sourcePath === "/main/subjectDataBase/pictureDataBase") {
        module = "专题数据/图片信息库";
      }else if (this.$route.query.sourcePath === "/main/subjectDataBase/videoDataBase") {
        module = "专题数据/视频信息库";
      }else if (this.$route.query.sourceName === "舆情提示") {
        module = "舆情提示/重点提示信息";
      }else if (this.$route.query.sourceName === "省办提示") {
        module = "舆情提示/省办提示";
      }
      this.historyLog(module, '信息详情页/'+detail.mtitle, detail.mkey);
    },
    CarouselChange(oldValue, newValue) {
      let num = this.details.pictureFiles.split(";").length;
      if (newValue === 0) {
        setTimeout(() => {
          this.CarouselKey++;
        }, 500);
      }
      if (oldValue === 0 && newValue === num - 1) {
        this.CarouselKey++;
      }
    },
    saveDetailEdit(formData, number, change) {
      if (change.mtitle) {
        this.details.mtitle = formData.mtitle;
      }
      if (change.mabstract) {
        this.details.mabstract = formData.mabstract;
        this.details.isUpdateMab = 1;
      }
      if (change.monitorName) {
        this.details.monitorName = formData.monitorName;
      }
      if (change.marea) {
        this.details.marea = formData.marea;
      }
      if (change.scopeArea) {
        this.details.scopeArea = formData.scopeArea;
      }
      if (change.msentiment) {
        this.details.msentiment = formData.msentiment;
      }
      if (change.mpersons) {
        this.details.mpersons = formData.mpersons;
      }
      if (change.mregion) {
        this.details.mregion = formData.mregion;
      }
      if (change.morganizations) {
        this.details.morganizations = formData.morganizations;
      }

      this.details.isTobeEditNumber = number;
      this.drawerVisi = false;
    },
    getTipStatus() {
      this.$http
        .get("/tip/getTipStatus", {
          params: {
            mkey: this.$route.query.msgKey,
          },
        })
        .then((res) => {
          if (res.body.status == 0) {
            this.$set(
              this.details,
              "tipStatus",
              res.body.data.tipStatus || null
            );
          }
        });
    },
    setProvincOfficeTips(type) {
      this.$http
        .post(
          "/tip/setTip",
          {
            mkey: this.$route.query.msgKey,
            situation: this.$route.query.situation,
            type: type,
          },
          { emulateJSON: true }
        )
        .then((res) => {
          if (res.body.status == 0) {
            this.$Message.success("提示成功！");
            this.getTipStatus();
          } else {
            this.$Message.error(res.body.message);
          }
        });
    },
    changeNumber(number) {
      this.isFirstInit = false;
      this.details.isTobeEditNumber = number;
      if (this.details.isTobeEditNumber > 0) {
        this.drawerVisi = true;
      }
    },
    GatherEvidence() {
      let type = 1;
      let params = {
        mkey: this.$route.query.msgKey, //数据mkey
        type: type, //1 涉济，2 涉鲁，3涉政
        evidenceType: 0,
      };
      this.$http
        .post("/recommend/obtainEvidenceMsgWithNoSucess", params, {
          emulateJSON: true,
        })
        .then((res) => {
          // if (res.body.status === 0) {
          //   this.$Message.warning("信息正在取证中，请稍后查看");
          //   this.datas.isEvidence = 1;
          // } else {
          //   this.$Message.error(res.body.message);
          // }
        });
    },
  },
  // 计算属性 类似于 data 概念
  computed: {
    httpHeader() {
      // console.log(decodeURIComponent(this.$route.query.moduleName))
      return !!this.$route.query.moduleName
        ? {
            sys_log_module: encodeURIComponent(
              decodeURIComponent(this.$route.query.moduleName)
            ),
          }
        : {};
    },
    isQx() {
      return this.$route.query.isQx == "1" ? true : false;
    },
  },
  // 监控 data 中的数据变化
  watch: {
    CarouselId(d) {
      console.log(d);
      if (d === 0) {
      }
    },
  },
  //过滤器
  filters: {},

  // 生命周期 - 挂载完成（可以访问 DOM 元素）
  mounted() {
    if(this.$route.query.showpop && this.$route.query.showpop == 1) {
      this.openAddAccountModal()
    }
    if (this.$route.query.sourceName === "搜索结果") {
      this.btnStatus = {
        collect: true,
        // 重点提示
        // point: true,
        //提示单按钮
        handelTips: true,
        // //推荐按钮
        // recommendMsg: true,
        // //生成摘要
        // createAbstract: true,
        // //创建事件按钮
        // createEvent: true,
        //加入素材库按钮
        addMaterial: true,
        // 复制链接
        // copyURL: true,
        //加入涉鲁信息库
        // JoinSLDatabase: true,
        //加入涉政信息库
        // JoinSZDatabase: true,
        // 删除
        // delMsg: true,
        // 下载
        // downLoad: true,
        // //精选
        // handpick: true,
        //预警推送
        warningPush: true,
        // //取证
        GatherEvidence: true,
        // // 查看取证
        LookEvidence: true,
        detectionStatus: true, // 探测
      };

      //this.GatherEvidence();
    } else {
      this.btnStatus = {
        // 收藏
        collect: true,
        // 重点提示
        point: true,
        //提示单按钮
        handelTips: true,
        //推荐按钮
        recommendMsg: true,
        //生成摘要
        createAbstract: true,
        //创建事件按钮
        createEvent: true,
        //加入素材库按钮
        addMaterial: true,
        // 复制链接
        // copyURL: true,
        //加入涉鲁信息库
        // JoinSLDatabase: true,
        //加入涉政信息库
        // JoinSZDatabase: true,
        // 删除
        delMsg: true,
        // 下载
        // downLoad: true,
        //精选
        handpick: true,
        //预警推送
        warningPush: true,
        //取证
        // GatherEvidence: true,
        // 查看取证
        // LookEvidence: true,
        detectionStatus: true, // 探测
      };
    }
    if (
      this.$route.query.sourceName === "涉济推荐" ||
      this.$route.query.sourceName === "舆情提示" ||
      this.$route.query.sourceName === "短视频监测"
    ) {
      this.btnStatus.GatherEvidence = true;
      this.btnStatus.LookEvidence = true;
    }
    if (this.$route.query.sourceName === "境外监测") {
      this.btnStatus = {};
    }
    // this.$route.meta.moduleName = this.$route.query.moduleName ? decodeURIComponent(this.$route.query.moduleName) : '';
  },
};
</script>

<style lang="less" scoped>
.details {
  .box {
    width: 1720px;
    margin-top: 20px;

    .InfoDetails {
      background-color: #fff;
      height: calc(~"100vh - 40px");
      overflow-y: auto;
      width: 1180px;
      border: 1px solid rgba(255, 255, 255, 0.29);
      border-radius: 8px;
      padding: 10px 14px;
      position: relative;

      .top {
        padding: 10px 0;
        border-bottom: 2px solid #eee;
      }
    }

    .RelevantInfo {
      width: 520px;

      /deep/ .outerFrame {
        box-shadow: none;
      }

      .tabBox {
        position: relative;

        .tab {
          background-color: #fff;
          text-align: center;
          width: 33%;
          line-height: 50px;
          font-weight: 600;

          &:nth-child(1) {
            clip-path: polygon(0% 0%, 100% 0%, 70% 100%, 0% 100%);
          }

          &:nth-child(2) {
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            width: 46%;
            clip-path: polygon(20% 0%, 100% 0%, 80% 100%, 0% 100%);
          }

          &:nth-child(3) {
            clip-path: polygon(30% 0%, 100% 0%, 100% 100%, 0% 100%);
          }
        }

        .active {
          background-color: #5092e2;
          color: #fff;
        }
      }

      .content {
        height: calc(~"100vh - 100px");
        overflow-y: auto;
        margin: 10px 0;
      }
    }
  }

  .demo-carousel {
    width: 900px;
    height: 600px;
    // background-color: red;
    margin: auto;
    background-color: #fff;

    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
      cursor: url("../../../assets/img/放大.png"), zoom-in;
    }
  }

  .ivu-carousel {
    margin: 20px;
  }
  .edit_content {
    position: fixed;
    right: 2px;
    top: 300px;
    z-index: 999;
    font-size: 16px;
    color: #fff;
    background-color: #2f88ff;
    width: 40px;
    padding: 10px 12px;
    border-top-left-radius: 6px;
    border-bottom-left-radius: 6px;
    box-shadow: -10px 0 5px rgba(0, 0, 0, 0.29);
    cursor: pointer;
    ::v-deep .ivu-badge-count {
      top: -5px;
      right: 3px;
      box-shadow: none;
    }
  }
}
.drawer_content {
  .edit_content {
    position: fixed;
    // left: 0;
    top: 300px;
    z-index: 999;
    font-size: 16px;
    color: #fff;
    background-color: #2f88ff;
    width: 40px;
    padding: 10px 12px;
    border-top-left-radius: 6px;
    border-bottom-left-radius: 6px;
    transform: translateX(-142%);
    box-shadow: -10px 0 5px rgba(0, 0, 0, 0.29);
    cursor: pointer;
    ::v-deep .ivu-badge-count {
      top: -5px;
      right: 3px;
      box-shadow: none;
    }
  }
}
</style>
