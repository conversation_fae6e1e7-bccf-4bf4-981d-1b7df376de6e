<template>
  <div class="infoList">
    <div class="exportbtn_box">
      <div class="exportbtn" @click="derive">
        <svg-icon icon-class="信息库-导出" />
        批量导出
      </div>
    </div>
    <div v-if="total !== 0" class="listBox">
      <div v-for="(item, index) in listData" :key="index" class="item">
        <div class="SerialNum">{{ index + 1 + (pageNo - 1) * pageSize }}.</div>
        <div class="content">
          <template v-if="item.situation !== 10">
            <div class="title ellipsis cp" @click="toDetails(item)">
            
              <PlatformIcon :id="item.situation" />
                  <span
                v-if="
                  item.isForward == 1
                "
                style="
                  color: #fff;
                  background: #FF8000;
                  border-radius: 4px;
                  font-size: 14px;
                "
                >已转发</span
              >
              <span
                v-html="
                  highlightTool.highlightByHitWords(
                    item.mtitle,
                    item.matchWords ? item.matchWords.split(',') : [],
                    'highlight0'
                  )
                "
              ></span>
            </div>
            <div class="abstract ellipsis cp" @click="toDetails(item)">
              <span
                v-html="
                  highlightTool.highlightByHitWords(
                    item.mabstract
                      ? removeROrN(item.mabstract)
                      : removeROrN(item.mcontent),
                    item.matchWords ? item.matchWords.split(',') : [],
                    'highlight0'
                  )
                "
              ></span>

              <!-- ocr内容展示 -->
              <span
                v-if="
                  (item.mabstract && item.mabstract.indexOf('\\nocr:') > -1) ||
                  (item.mcontent && item.mcontent.indexOf('\\nocr:') > -1)
                "
                style="margin-left: 5px;"
              >
                <svg-icon icon-class="ocr-img" />
                <span
                  v-html="
                    highlightTool.highlightByHitWords(
                      item.mabstract
                        ? removeOcr(item.mabstract)
                        : removeOcr(item.mcontent),
                      item.matchWords ? item.matchWords.split(',') : [],
                      'highlight0'
                    )
                  "
                ></span>
              </span>
            </div>
          </template>
          <template v-else>
            <div
              class="abstract ellipsis-2 cp"
              style="height: 48px;"
              @click="toDetails(item)"
            >
              <PlatformIcon :id="item.situation" />
                <span
                v-if="
                  item.isForward == 1
                "
                style="
                  color: #fff;
                  background: #FF8000;
                  border-radius: 4px;
                  font-size: 14px;
                "
                >已转发</span
              >
              <span
                v-html="
                  highlightTool.highlightByHitWords(
                    item.mtitle
                      ? removeROrN(item.mtitle)
                      : removeROrN(item.mcontent),
                    item.matchWords ? item.matchWords.split(',') : [],
                    'highlight0'
                  )
                "
              ></span>
              <!-- ocr内容展示 -->
              <span
                v-if="
                  (item.mtitle && item.mtitle.indexOf('\\nocr:') > -1) ||
                  (item.mcontent && item.mcontent.indexOf('\\nocr:') > -1)
                "
                style="margin-left: 5px;"
              >
                <svg-icon icon-class="ocr-img" />
                <span
                  v-html="
                    highlightTool.highlightByHitWords(
                      item.mtitle
                        ? removeOcr(item.mtitle)
                        : removeOcr(item.mcontent),
                      item.matchWords ? item.matchWords.split(',') : [],
                      'highlight0'
                    )
                  "
                ></span>
              </span>
            </div>
          </template>
          <div class="remarksOperations">
            <div class="remarks">
              <div>
                {{ item.mpublishTime }}
              </div>
              <div>
                <svg-icon icon-class="媒体类型" />
                {{
                (situation[item.situation] && item.situation != 600 && item.situation != 80 ? situation[item.situation] : "") +
                (situation[item.situation] && item.situation != 600 && item.situation != 80 ? "-" : "") +
                  (item.mwebsiteName ? item.mwebsiteName : "")
                }}
              </div>
              <div v-if="item.uname">
                <Icon type="ios-contact" />
                {{ item.uname }}
              </div>
              <div v-if="item.mlocIssue && item.mareaIssue">
                <svg-icon icon-class="location" />
                {{ item.mlocIssue + ' ' + item.mareaIssue }}
              </div>
            </div>
            <ListControls itemMargin="10px" :btnStatus="btnStatus" :data="item" />
          </div>
        </div>
      </div>
    </div>
    <Spin v-if="loading" fix>
      <Icon class="demo-spin-icon-load" size="18" type="ios-loading"></Icon>
      <div>Loading</div>
    </Spin>
    <no-data v-if="total === 0 && !loading" />

    <Page
      v-show="total > 0 && !loading"
      :current="pageNo"
      :page-size="pageSize"
      :total="total"
      show-elevator
      show-sizer
      show-total
      @on-change="pageNoChange"
      @on-page-size-change="pageSizeChange"
    />
  </div>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具 js，第三方插件 js，json 文件，图片文件等等）
// 例如：import  《组件名称》  from '《组件路径》 ';
import highlightTool from "trs-tool-highlight";

import ListControls from "@/components/listControls/index.vue";
import PlatformIcon from "@/components/platformIcon/index.vue";
import situation from "@/assets/json/situations";
// const situation = {
//   0: "全部",
//   30: "媒体网站",
//   31: "客户端",
//   10: "微博",
//   20: "微信公号",
//   80: "小红书",
//   61: "论坛",
//   62: "贴吧",
//   170: "知乎",
//   199: "短视频",
//   200: "短视频",
//   230: "自媒体",
// };
export default {
  data() {
    // 这里存放数据
    return {
      situation,
      listData: [],
      btnStatus: { recommendMsg: true, addMaterial: true, handelTips: true },
      pageNo: 1,
      pageSize: 20,
      loading: true,
      total: 0,
    };
  },
  // import 引入的组件需要注入到对象中才能使用
  components: { PlatformIcon, ListControls },
  props: {
    params: {
      default: {},
    },
  },
  // 生命周期 - 创建完成（可以访问当前this 实例）
  created() {},
  // 方法集合
  methods: {
    removeOcr(str) {
      if (str) {
        if (str.indexOf("\\nocr:") > -1) {
          str = str.substring(str.indexOf("\\nocr:"), str.length);
        }
        return str
          .replace(/(\\nocr:)/g, "")
          .replace(/(\r\n|\n|\r|\\r\\n|\\r|\\n)/g, "")
          .replace(/<[^>]+>/g, "")
          .trim();
      } else {
        return "";
      }
    },
    removeROrN(str) {
      if (str) {
        return str
          .replace(/(\r\n|\n|\r|\\r\\n|\\r|\\n)/g, "")
          .replace(/<[^>]+>/g, "")
          .trim();
      } else {
        return "";
      }
    },
    toDetails(d) {
      const { href } = this.$router.resolve({
        path: "/main/details",
        query: {
          msgKey: d.mkey,
          keyword: d.matchWords,
          situation: d.situation,
          sourcePath: this.$route.path,
          sourceName: this.$route.name,
              moduleName: this.$route.meta && this.$route.meta.moduleName ? encodeURIComponent(this.$route.meta.moduleName) : '',
          moduleOriginName: this.$route.meta && this.$route.meta.moduleName ? encodeURIComponent(this.$route.meta.moduleName) : ''
        },
      });
      window.open(href, "_blank");
    },
    pageSizeChange(d) {
      this.pageNo = 1;
      this.pageSize = d;
      this.getCount();
    },
    pageNoChange(d) {
      this.pageNo = d;
      this.getListData();
    },
    getCount() {
      this.pageNo = 1;
      this.listData = [];
      this.total = 0;
      let ruleIds = this.params.ruleIds ? this.params.ruleIds : [];
      if (this.params.typeId == 3 && !ruleIds) {
        this.$Message.warning("请至少勾选一项");
        return;
      }
      this.loading = true;
      let params = {
        ...this.params,
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        ruleIds: ruleIds,
      };
      if (params.typeId === "2") {
        params.murl = params.keyword;
        params.keyword = null;
      }
      this.$http
        .post("/search/msgCount", params)
        .then((res) => {
          console.log(res);
          this.loading = false;
          if (res.body.status === 0 && res.body.data) {
            this.total = res.body.data.total;
            // this.getListData();
            this.listData = !res.body.data.list ? [] : res.body.data.list;
            this.btnStatus = {
              recommendMsg: true,
              addMaterial: true,
              handelTips: true,
            };
          } else {
            this.total = 0;
            this.listData = [];
          }
        })
        .catch((err) => {
          this.loading = false;
          this.$Message.error("服务器错误！");
        });
      this.getLog("涉济监测/精确搜索","搜索/"+params.keyword);
    },
    getListData() {
      this.listData = [];
      this.loading = true;
      let params = {
        ...this.params,
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        ruleIds: this.params.ruleIds ? this.params.ruleIds : [],
        emotions:
          this.params.emotions === "3"
            ? 0
            : this.params.emotions === "2"
            ? -1
            : this.params.emotions === "1"
            ? 1
            : null,
      };
      if (params.typeId === "2") {
        params.murl = params.keyword;
        params.keyword = null;
      }
      this.$http
        .post("/search/msgList", params)
        .then((res) => {
          console.log(res);
          if (res.body.status === 0) {
            this.listData = res.body.data;
          }
          this.loading = false;
          this.btnStatus = {
            recommendMsg: true,
            addMaterial: true,
            handelTips: true,
          };
        })
        .catch((err) => {
          this.loading = false;
          this.$Message.error("服务器错误！");
        });
    },
    derive() {
      let params = {
        ...this.params,
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        ruleIds: this.params.ruleIds ? this.params.ruleIds : [],
        emotions:
          this.params.emotions === "3"
            ? 0
            : this.params.emotions === "2"
            ? -1
            : this.params.emotions === "1"
            ? 1
            : null,
      };
      if (params.typeId === "2") {
        params.murl = params.keyword;
        params.keyword = null;
      }
      this.$http
        .post("/search/exportMsgByKeyWord", params, {
         
          responseType: "blob",
        })
        .then((res) => {
          const disposition = res.headers.get("Content-Disposition");
          let filename = "downloaded_file";
          if (disposition && disposition.indexOf("attachment") !== -1) {
            const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
            const matches = filenameRegex.exec(disposition);
            if (matches != null && matches[1]) {
              filename = matches[1].replace(/['"]/g, "");
              filename = decodeURIComponent(escape(filename)); // 解码文件名
            }
          }
          const blob = new Blob([res.body]);
          const a = document.createElement("a");
          a.href = URL.createObjectURL(blob);
          a.download = filename;
          document.body.appendChild(a);
          a.click();

          setTimeout(() => {
            document.body.removeChild(a);
            URL.revokeObjectURL(a.href);
          }, 0);
        });
    }
  },
  // 计算属性 类似于 data 概念
  computed: {
    highlightTool() {
      return highlightTool;
    },
  },
  // 监控 data 中的数据变化
  watch: {
    params: {
      handler(d) {
        if (d) {
          this.getCount();
        } else {
          this.loading = false;
        }
      },
      deep: true,
    },
  },
  //过滤器
  filters: {},

  // 生命周期 - 挂载完成（可以访问 DOM 元素）
  mounted() {
    console.log(this.params, this.$route.path, "this.$route.path");
  },
};
</script>

<style lang="less" scoped>
.infoList {
  background-color: #fff;
  margin-top: 20px;
  border-radius: 8px;
  position: relative;
  padding: 20px;
  //height: calc(~"100vh - 330px");
  height: calc(~"100vh - 250px");

  .listBox {
    //height: calc(~"100vh - 410px");
    height: calc(~"100vh - 380px");
    padding: 10px 5px;
    overflow-y: auto;
  }

  .item {
    display: flex;
    justify-content: space-between;
    height: 130px;
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.16);
    margin-top: 12px;
    padding: 10px;
  }

  .content {
    font-size: 16px;
    width: calc(~"100% - 30px");

    .title {
      font-weight: 600;
      line-height: 22px;
    }

    .remarksOperations {
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: #666666;
      font-size: 14px;
      margin-top: 20px;

      .remarks {
        display: flex;
        line-height: 22px;

        & > div {
          margin-right: 30px;
          display: flex;
          align-items: center;
        }

        .line {
          width: 0;
          height: 22px;
          border: 1px solid #707070;
        }
      }
    }
  }

  .exportbtn_box{
    display: flex;
    justify-content: flex-end;
      .exportbtn {
      background: #5585ec;
      border-radius: 4px;
      line-height: 30px;
      height: 30px;
      padding: 0 18px;
      color: #fff;
      margin-right: 20px;
      cursor: pointer;
      
    }
  }
}
</style>
