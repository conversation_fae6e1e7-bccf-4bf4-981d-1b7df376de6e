<template>
  <div>
    <!-- <div class="remark flex">
      {{editInfo.isCollect}}/{{editInfo.collectCount}}/{{editInfo.isComment}}/{{editInfo.commentCount}}
      <div class="item" @click="openModal">
        <svg-icon icon-class="浏览" />
        {{ editInfo.visitCount }}
      </div>
      <div class="item">
        <svg-icon
          :icon-class="editInfo.isLike ? '点赞_块蓝' : '点赞_块'"
          @click.native="remarkControls(2, 'isLike', 'likeCount')"
        />{{ editInfo.likeCount }}
      </div>
      <div class="item" @click="commentsSection = !commentsSection">
        <svg-icon :icon-class="editInfo.isComment ? '评论蓝' : '评论'" />{{
          commentsSection ? "收起评论" : editInfo.commentCount
        }}
      </div>
      <div class="item">
        <svg-icon
          :icon-class="editInfo.isCollect ? '收藏蓝' : '收藏'"
          @click.native="remarkControls(3, 'isCollect', 'collectCount')"
        />{{ editInfo.collectCount }}
      </div>
    </div> -->
    <div class="commentsSection" v-if="commentsSection">
      <div class="publish flex">
        <svg-icon
          icon-class="默认头像"
          style="width:32px;height:32px;margin-right:10px;margin-top:10px"
        />
        <!-- <div class="author ellipsis">
          {{ currentUser }}
        </div> -->
        <div class="textInput">
          <Input
            v-model="ToBeReleased"
            type="textarea"
            :autosize="true"
            placeholder="理性发言，友善互动"
            style="width: 630px;"
          />
          <div
            class="btn cp"
            style="margin-left: 20px;"
            @click="PostReview"
          >
            发布
          </div>
        </div>
      </div>
      <div class="reviewList">
        <div class="count">{{ editInfo.commentCount }}条评论</div>
        <div class="list">
          <div v-if="!loading && editInfo.commentCount == 0" class="nodata">
            <div class="nodataImg"></div>
            <span >还没有评论，发表第一个评论吧</span>
          </div>
          <!-- <NoData
            v-if="!loading && editInfo.commentCount == 0"
            style="color:#E1E1E1"
            text="还没有评论，发表第一个评论吧"
          /> -->
          <Spin style="margin: 100px;" v-if="loading">
            <Icon
              type="ios-loading"
              size="18"
              class="demo-spin-icon-load"
            ></Icon>
            <div>Loading</div>
          </Spin>
          <div class="item" v-for="i in reviewList" :key="i.id">
            <div class="head flex">
              <svg-icon
                icon-class="默认头像"
                style="width:24px;height:24px;margin-right:8px"
              />
              <div class="name">
                {{ i.organName + "-" + i.createUserName }}
              </div>
              <div class="time" style="color:#888888">
                {{ moment(i.createTime).format("YYYY-MM-DD HH:mm:ss") }}
              </div>
            </div>
            <div class="text">
              {{ i.comment }}
            </div>
          </div>
          <Page
            v-if="editInfo.commentCount > 0 && !loading"
            :total="editInfo.commentCount"
            :current="pageNo"
            :page-size="pageSize"
            show-total
            @on-change="pageChange"
          />
        </div>
      </div>
    </div>
    <Modal v-model="readModalStatus" width="800px" title="阅读人员">
      <div class="modal">
        <div class="header flex sb">
          <div class="item">
            单位
          </div>
          <div class="item">
            人员
          </div>
          <div class="item">
            最新阅读时间<Icon type="md-arrow-round-down" />
          </div>
        </div>
        <NoData v-if="!modalLoading && editInfo.visitCount == 0" />
        <Spin style="margin: 100px;" v-if="modalLoading">
          <Icon type="ios-loading" size="18" class="demo-spin-icon-load"></Icon>
          <div>Loading</div>
        </Spin>
        <div class="list" v-if="!modalLoading && editInfo.visitCount > 0">
          <div class="row flex sb" v-for="i in modalList" :key="i.id">
            <div class="item">
              {{ i.organName }}
            </div>
            <div class="item">
              {{ i.createUserName }}
            </div>
            <div class="item">
              {{ moment(i.createTime).format("YYYY-MM-DD HH:mm:ss") }}
            </div>
          </div>
        </div>

        <Page
          v-if="editInfo.visitCount > 0 && !modalLoading"
          :total="editInfo.visitCount"
          :current="modalPageNo"
          :page-size="modalPageSize"
          @on-change="modalPageNoChange"
          show-total
        />
      </div>
      <div slot="footer"></div>
    </Modal>
  </div>
</template>

<script>
import moment from "moment";
export default {
  name: "",
  data() {
    return {
      moment,
      ToBeReleased: "", //待发布的评论
      // commentsSection: false, //评论区是否展开
      reviewList: [],
      pageNo: 1,
      pageSize: 10,
      modalPageNo: 1,
      modalPageSize: 10,
      editInfo: {},
      loading: false, //评论区是否加载中
      readModalStatus: false, //阅读弹窗
      modalLoading: false, //阅读弹窗加载中
      modalList: [],
      currentUser:
        localStorage.getItem("organName") +
        "-" +
        localStorage.getItem("userName"),
    };
  },
  props: {
    info: {},
    updateList: {
      type: Function,
      default: () => {},
    },
    commentsSection: {
      type: Boolean,
      default: false,
    }
  },
  watch: {
    info(d) {
      this.editInfo = d;
    },
    commentsSection(d) {
      if (d) {
        this.getCommentList();
      }
    },
    reviewList: {
      deep: true,
      handler(newVal, oldVal) {
        if (newVal.length > 0) {
          this.commentRead();
        }
      },
    },
  },
  computed: {},
  methods: {
    commentRead() {
      let params = new FormData();
      let ids = this.reviewList.map((i) => i.id);
      params.append("artworkId", this.info.artworkId);
      params.append("commentIds", ids.toString());
      this.$http.post("/learning/commentRead", params).then((res) => {
        console.log(res);
      });
    },
    pageChange(d) {
      this.pageNo = d;
      this.getCommentList();
    },
    modalPageNoChange(d) {
      this.modalPageNo = d;
      this.getLikeList();
    },
    modalPageChange(d) {
      this.modalPageNo = d;
      this.getLikeList();
    },
    openModal() {
      this.readModalStatus = true;
      this.modalPageNo = 1;
      this.modalList = [];
      this.getLikeList();
    },
    getLikeList() {
      this.modalLoading = true;
      this.$http
        .get("/learning/getLikeList", {
          params: {
            artworkIds: this.info.artworkId,
            type: 1,
            pageNo: this.modalPageNo,
            pageSize: this.modalPageSize,
          },
        })
        .then((res) => {
          this.modalList = res.body.data;
          this.modalLoading = false;
        });
    },
    remarkControls(id, type, typeCount) {
      let params = new FormData();
      params.append("artworkId", this.info.artworkId);
      params.append("type", id);
      params.append("cancel", this.editInfo[type] === 1);
      this.$http.post("/learning/operateLike", params).then((res) => {
        console.log(res);
        if (res.body.status === 0) {
          if (this.editInfo[type] == 1) {
            this.editInfo[typeCount]--;
            this.editInfo[type] = 0;
          } else {
            this.editInfo[typeCount]++;
            this.editInfo[type] = 1;
          }
        }
      });
    },
    PostReview() {
      if (!this.ToBeReleased) {
        this.$Message.error("请输入评论内容");
        return;
      }
      let params = new FormData();
      params.append("artworkId", this.info.artworkId);
      params.append("content", this.ToBeReleased);
      this.$http
        .post("/learning/addComment", params)
        .then((res) => {
          this.ToBeReleased = "";
          this.editInfo.commentCount++;
          this.editInfo.isComment = 1;
          this.getCommentList();
          this.updateList();
        })
        .catch((e) => {
          this.$message.error("评论失败");
        });
    },
    getCommentList() {
      if (this.editInfo.commentCount == 0) return (this.loading = false);
      this.loading = true;
      this.reviewList = [];
      let params = {
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        artworkIds: this.info.artworkId,
      };
      this.$http.get("/learning/getCommentList", { params }).then((res) => {
        console.log(res);
        this.loading = false;
        this.reviewList = res.body.data;
      });
    },
  },
  mounted() {
    this.editInfo = this.info;
  },
};
</script>

<style lang="less" scoped>
.modal {
  .item {
    text-align: center;
    border-right: 1px solid #d7d7d7;
    border-bottom: 1px solid #d7d7d7;
    flex: 1;
  }
  .header {
    border-top: 1px solid #d7d7d7;
    border-left: 1px solid #d7d7d7;
    font-size: 18px;
    font-weight: 600;
    height: 50px;
    line-height: 50px;
  }
  .list {
    height: 400px;
    font-size: 16px;
    .row {
      border-top: 1px solid #d7d7d7;
      border-left: 1px solid #d7d7d7;
      height: 40px;
      line-height: 40px;
    }
  }
}
.remark {
  .item {
    margin-right: 20px;
    font-size: 12px;
    cursor: pointer;
    .svg-icon {
      margin-right: 5px;
      font-size: 14px;
    }
  }
}
.commentsSection {
  .publish {
    display: flex;
    align-items: flex-start;
    .author {
      width: 200px;
      line-height: 20px;
      margin-top: 20px;
    }
    .textInput {
      display: flex;
      align-items: flex-start;
      margin: 10px 0;
    }
  }
  .reviewList {
    border: 1px solid #d7d7d7;
    // width: 550px;
    width: 782px;
    .nodata {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      span {
        margin-bottom: 100px;
        font-size: 14px;
        color: #737B80;
        line-height: 30px;
      }
    }
    .nodataImg {
      margin-top: 103px;
      width: 280px;
      height: 130px;
      background: url('../../../../assets/img/nomark.png') no-repeat;
      
    }
    .count {
      font-size: 14px;
      font-weight: 600;
      padding: 0 20px;
      line-height: 40px;
      border-bottom: 1px solid #e5e5e5;
    }
    .list {
      max-height: 500px;
      overflow-y: auto;
      .item {
        border-bottom: 1px solid #e5e5e5;
        padding: 20px;
        font-size: 14px;
        .head {
          .name {
            font-weight: 600;
          }
          .time {
            margin-left: 20px;
          }
        }
        .text {
          margin-top: 10px;
        }
      }
    }
  }
}
/deep/.ivu-input {
  background-color: #F4F6F9;
  border-radius: 2px;
}
</style>
