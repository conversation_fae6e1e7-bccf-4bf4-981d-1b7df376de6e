<template>
  <div class="markwrap">
    <div class="publishInfo flex">
      <div class="author">发布人：{{ name }}</div>
      <div class="time">
        {{ moment(time).format("YYYY-MM-DD HH:mm:ss") }}
      </div>
      <div class="del" v-if="delStatus">
        <svg-icon icon-class="删除-aaa" @click.native="del" />
      </div>
    </div>
    
    <div class="remark flex">
      <div class="item" @click="openModal">
        <svg-icon icon-class="浏览" />
        {{ editInfo.visitCount }}
      </div>
      <div class="item">
        <svg-icon
          :icon-class="editInfo.isLike ? '点赞_块蓝' : '点赞_块'"
          @click.native="remarkControls(2, 'isLike', 'likeCount')"
        />{{ editInfo.likeCount }}
      </div>
      <div class="item" @click="showComment()">
        <svg-icon :icon-class="editInfo.isComment ? '评论蓝' : '评论'" />{{
          commentsSection ? "收起评论" : editInfo.commentCount
        }}
      </div>
      <div class="item">
        <svg-icon
          :icon-class="editInfo.isCollect ? '收藏蓝' : '收藏'"
          @click.native="remarkControls(3, 'isCollect', 'collectCount')"
        />{{ editInfo.collectCount }}
      </div>
    </div>
    <Modal v-model="modal" title="删除确认" v-if="delStatus">
      <p class="title">
        是否删除知识“<span>{{ title }}</span
        >”？
      </p>
      <div slot="footer">
        <div class="btn" @click="verify">
          确认
        </div>
        <div class="btn off" @click="modal = false">
          关闭
        </div>
      </div>
    </Modal>
  </div>
</template>

<script>
import moment from "moment";
export default {
  name: "",
  data() {
    return {
      moment,
      modal: false,
      delStatus: false,
      commentsSection: false, //评论区是否展开
      editInfo: {},
      isshowtxt:false,
    };
  },
  props: {
    info: {},
    name: {
      type: String,
      default: "",
    },
    time: {
      default: null,
    },
    title: {
      type: String,
      default: "",
    },
    createUser: {
      type: String,
      default: "",
    },
  },
  methods: {
    showComment() {
      this.commentsSection = !this.commentsSection
      this.$emit('showComment')
    },
    del() {
      this.modal = true;
    },
    verify() {
      this.$emit("del");
      this.modal = false;
    },
    
    openModal() {
      this.readModalStatus = true;
      this.modalPageNo = 1;
      this.modalList = [];
      this.getLikeList();
    },
    remarkControls(id, type, typeCount) {
      let params = new FormData();
      console.log('this.infothis.info',this.info);
      params.append("artworkId", this.info.artworkId);
      params.append("type", id);
      params.append("cancel", this.editInfo[type] === 1);
      this.$http.post("/learning/operateLike", params).then((res) => {
        console.log(res);
        if (res.body.status === 0) {
          if (this.editInfo[type] == 1) {
            this.editInfo[typeCount]--;
            this.editInfo[type] = 0;
          } else {
            this.editInfo[typeCount]++;
            this.editInfo[type] = 1;
          }
        }
      });
    },
  },
  mounted() {
    let str = "test,chensu3,bijianjun,dingtong,caoyang,yaoxuqian";
    let userAccount = localStorage.getItem("userAccount");

    this.delStatus =
      str.split(",").indexOf(userAccount) > -1 || this.createUser == userAccount;
    this.editInfo = this.info;
    
  },
  watch: {
    info(d) {
      this.editInfo = d;
    },
  },
};
</script>

<style lang="less" scoped>
.markwrap {
  display: flex;
  align-items: center;
}
.remark {
  .item {
    margin-right: 20px;
    font-size: 12px;
    cursor: pointer;
    .svg-icon {
      margin-right: 5px;
      font-size: 14px;
    }
  }
}
.publishInfo {
  padding-right: 30px;
  font-size: 16px;
  color: #aaa;
  margin: 10px 0;
  border-right:1px solid #aaa;
  margin-right: 30px;

  .time {
    margin-left: 20px;
  }
  .del {
    margin-left: 20px;
    cursor: pointer;
  }
}
.title {
  text-align: center;
  font-size: 16px;
  span {
    font-weight: 700;
    font-size: 20px;
  }
}
.btn {
  display: inline-block;
}
.off {
  background-color: #999;
}
</style>
