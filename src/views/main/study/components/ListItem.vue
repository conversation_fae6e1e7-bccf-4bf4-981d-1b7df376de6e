<template>
  <div class="listItem">
    <Title
      class="cp"
      @toDetail="toDetail"
      :title="info.msgTitle"
      :filePaths="getFileType(info.filePaths)"
    />
    <div class="content cp flex">
      <div class="medium cp" v-if="firstImagePath" @click="viewMedium">
        <img :src="imageBaseUrls + firstImagePath" alt="" />
      </div>
      <div class="medium cp" @click="viewMedium" v-if="firstVideoPath">
        <!-- preload="none" -->
        <video style="width: 100%; height: 100%;">
          <source :src="imageBaseUrls + firstVideoPath" type="video/mp4" />
        </video>
      </div>

      <div
        :class="['text', firstImagePath ? 'ellipsis-4' : 'ellipsis-2']"
        @click="toDetail"
      >
        {{ info.msgContent }}
      </div>
    </div>
    <div class="newbox">
      <PublishInfo
        :name="info.organName + '-' + info.createUserName"
        :time="info.createTime"
        :title="info.msgTitle"
        :info="info"
        :createUser="info.createUserId"
        @del="del"
        @showComment="commentsSection = !commentsSection"
      />
      <Remark :info="info" :commentsSection="commentsSection"/>
    </div>
    
  </div>
</template>

<script>
import Remark from "./Remark";
import Title from "./Title";
import PublishInfo from "./publishInfo";
export default {
  name: "",
  data() {
    return {
      imageBaseUrls: gl.minioUrl,
      commentsSection: false,
    };
  },
  props: {
    info: { type: Object, default: () => {} },
  },
  components: {
    Remark,
    Title,
    PublishInfo,
  },
  computed: {
    // 新增计算属性用于挑出一张图片
    firstImagePath() {
      if (this.info.filePaths == null || this.info.filePaths == "") {
        return false;
      }
      const imageExtensions = [".jpeg", ".png", ".gif", ".jpg"];
      const filePathsArray = this.info.filePaths.split(",");
      // 过滤出符合扩展名的文件路径
      return filePathsArray.find((path) => {
        const ext = path.slice(path.lastIndexOf(".")).toLowerCase();
        console.log(this.imageBaseUrl, imageExtensions.includes(ext));
        return imageExtensions.includes(ext);
      });
    },
    firstVideoPath() {
      if (this.info.filePaths == null || this.info.filePaths == "") {
        return false;
      }
      const imageExtensions = [".avi", ".mp4", ".mov", ".wmv"];
      const filePathsArray = this.info.filePaths.split(",");
      // 过滤出符合扩展名的文件路径
      return filePathsArray.find((path) => {
        const ext = path.slice(path.lastIndexOf(".")).toLowerCase();
        console.log(this.imageBaseUrl, imageExtensions.includes(ext));
        return imageExtensions.includes(ext);
      });
    },
  },
  methods: {
    del() {
      let params = new FormData();
      params.append("artworkId", this.info.artworkId);
      this.$http.post("/learning/delArtwork", params).then((res) => {
        if (res.body.data === 1) {
          this.$Message.success("删除成功");
          this.$emit("updataList");
        }
      });
    },
    getFileType(fileString) {
      if (!fileString) return [];
      const filePaths = fileString.split(",");
      console.log(filePaths);
      const fileTypeMap = {
        word: [".docx", ".doc"],
        excel: [".xlsx", ".xls"],
        pptx: [".pptx", ".ppt"],
        pdf: [".pdf"],
      };

      return filePaths
        .map((filePath) => {
          // 提取文件后缀
          const extensionMatch = filePath.match(/\.\w+$/);
          const extension = extensionMatch ? extensionMatch[0] : null;

          if (!extension) return null;

          // 查找文件类型
          for (const [key, extensions] of Object.entries(fileTypeMap)) {
            if (extensions.includes(extension)) {
              return { file: filePath, type: key };
            }
          }

          return null; // 如果没有匹配的类型，返回 null
        })
        .filter((item) => item !== null); // 过滤掉没有匹配的文件
    },
    toDetail() {
      const { href } = this.$router.resolve({
        path: "/main/study/detail",
        query: {
          artworkId: this.info.artworkId,
        },
      });
      let params = new FormData();
      params.append("artworkId", this.info.artworkId);
      params.append("type", 1);
      params.append("cancel", false);
      this.$http.post("/learning/operateLike", params);
      window.open(href, "_blank");
    },
    viewMedium() {
      console.log(
        this.imageBaseUrls + (this.firstImagePath || this.firstVideoPath)
      );
      this.$emit(
        "viewMedium",
        this.imageBaseUrls + (this.firstImagePath || this.firstVideoPath),
        this.firstVideoPath ? "video" : "img"
      );
    },
  },
};
</script>

<style lang="less" scoped>
.listItem {
  margin-bottom: 12px;
  // border-bottom: 1px solid #e5e5e5;
  padding: 12px 20px;
  // background: #FFFFFF;
  border-radius: 8px;
  box-shadow: 0px 2px 4px 0px rgba(0,0,0,0.16);
  .newbox {
    display: flex;
    flex-direction: column;
  }
  .content {
    font-size: 16px;

    .medium {
      width: 200px;
      height: 100px;
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
    .text {
      flex: 1;
      padding: 0 20px;
      line-height: 25px;
    }
  }
}
</style>
