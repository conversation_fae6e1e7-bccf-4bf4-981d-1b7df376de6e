<template>
  <div class="IndividualCenter">
    <div class="header flex sb">
      <div class="title">
        <Icon size="24" color="#5481EA" type="md-person" />个人中心
      </div>
      <div class="btn cp" @click="publishModalStatus = true">
        知识发布
      </div>
    </div>
    <div class="content">
      <div class="title flex">
        <svg-icon icon-class="标题-点"/>
        <span style="margin-left:12px">
          数据统计
        </span>
      </div>
      <div class="dataStatistics flex sb">
        <div class="item public" style="width: 48%;">
          <div class="newitem">
            <div class="title">
              知识发布数
            </div>
            <svg-icon icon-class="统计" style="width:30px;height:9px"/>
            <div class="num">
              {{ DataStatistics.artworkCount || 0 }}
            </div>
          </div>
          <svg-icon icon-class="知识发布数" style="width:35px;height:35px"/>
        </div>
        <div class="item  readpeo" style="width: 48%;">
          <div class="newitem">
            <div class="title">
              知识被阅读人数
            </div>
            <svg-icon icon-class="统计" style="width:30px;height:9px"/>
            <div class="num">
              {{ DataStatistics.visitCount || 0 }}
            </div>
          </div>
          <svg-icon icon-class="阅读人数" style="width:35px;height:35px"/>
        </div>
        <div class="item like" style="width: 31%;">
          <div class="newitem">
            <div class="title">
              知识被点赞数
            </div>
            <svg-icon icon-class="统计" style="width:30px;height:9px"/>
            <div class="num">
              {{ DataStatistics.likeCount || 0 }}
            </div>
          </div>
        </div>
        <div class="item mark" style="width: 31%;">
          <div class="newitem">
            <div class="title">
              知识被评论数
            </div>
            <svg-icon icon-class="统计" style="width:30px;height:9px"/>
            <div class="num">
              {{ DataStatistics.commentCount || 0 }}
            </div>
          </div>
        </div>
        <div class="item collect" style="width: 31%;">
          <div class="newitem">
            <div class="title">
              知识被收藏数
            </div>
            <svg-icon icon-class="统计" style="width:30px;height:9px"/>
            <div class="num">
              {{ DataStatistics.collectCount || 0 }}
            </div>
          </div>
        </div>
      </div>
      <div class="title flex">
        <svg-icon icon-class="标题-点"/>
        <span style="margin-left:12px">
          通知消息
        </span>
      </div>
      <div class="notification">
        <div class="result">
          <div class="item">
            <span style="color: #5482EA;"> {{ artworkUnreadCount || 0 }} </span><span style="color: #333333;">未读知识</span> 
          </div> 
          <div class="item">
            <span style="color: #5482EA;"> {{ commentUnreadCount || 0 }} </span ><span style="color: #333333;">未读评论</span>
          </div>
        </div>
        <div class="infoList">
          <div
            class="item ellipsis-2"
            v-for="i in commentUnreadList"
            :key="i.id"
          >
          <svg-icon icon-class="知识库" style="margin-top:4px;margin-right:10px"/>
          <div style="flex:1">
            {{ i.organName + "-" + i.createUserName }}
            <span class="cp" @click="toDetail(i.artworkId)">
              {{ i.msgTitle }}
            </span>
          </div>
            
          </div>
          <div class="seeMore">
            <span class="cp" @click="toMessageList(1)">
              点击查看全部>>
            </span>
          </div>
        </div>
      </div>
      <div class="title flex">
        <svg-icon icon-class="标题-点"/>
        <span style="margin-left:12px">
          知识收藏
        </span>
      </div>
      <div class="collect">
        <div
          class="item cp ellipsis-2"
          v-for="i in collectList"
          :key="i.artworkId"
          @click="toDetail(i.artworkId)"
        >
          <div style="display:flex">
            <svg-icon style="margin-top:4px;margin-right:8px" v-show="typeList[i.type] === '培训视频'" icon-class="小视频"/>
            <svg-icon style="margin-top:4px;margin-right:8px" v-show="typeList[i.type] === '图片'" icon-class="小图片"/>
            <svg-icon style="margin-top:4px;margin-right:8px" v-show="typeList[i.type] === '文件资料'" icon-class="小文件"/>
            <div style="flex:1">{{ i.msgTitle }}</div>
          </div>
          
          
        </div>
        <div class="seeMore">
          <span class="cp" @click="toMessageList(2)" >
            点击查看全部>>
          </span>
        </div>
      </div>
    </div>
    <Modal v-model="publishModalStatus" width="1000px" title="知识发布">
      <div class="modal">
        <div class="item flex">
          <div class="label flex">
            <span>
              标题：
            </span>
          </div>
          <div class="content">
            <Input v-model="title" />
          </div>
          <div class="remark"></div>
        </div>
        <div class="item flex">
          <div class="label flex">
            <span>
              内容：
            </span>
          </div>
          <div class="content">
            <Input v-model="content" type="textarea" :rows="20" />
          </div>
          <div class="remark label flex">(选填)</div>
        </div>
        <div class="item flex">
          <div class="label flex">
            <span>
              类型：
            </span>
          </div>
          <div class="content">
            <RadioGroup v-model="type">
              <Radio label="1">心得体会</Radio>
              <Radio label="2">图片</Radio>
              <Radio label="3">培训视频</Radio>
              <Radio label="4">文件资料</Radio>
            </RadioGroup>
          </div>
          <div class="remark"></div>
        </div>
        <div class="item flex" v-if="type > 1">
          <div class="label flex">
            <span> {{ typeList[type] }}： </span>
          </div>
          <div class="content flex">
            <Upload
              :before-upload="handleUpload"
              action="//jsonplaceholder.typicode.com/posts/"
              :accept="acceptList[type]"
            >
              <Button icon="ios-cloud-upload-outline"
                >上传{{ typeList[type] }}</Button
              >
            </Upload>
          </div>
          <div class="remark"></div>
        </div>
        <div class="fileList">
          <div
            v-for="(i, index) in file"
            :key="i.name"
            style="line-height: 30px; margin-left: 20px;"
          >
            {{ i.name
            }}<Icon @click.native="delFile(index)" class="cp" type="md-close" />
          </div>
        </div>
      </div>
      <div slot="footer">
        <div class="btnFrame flex">
          <div class="btn" @click="knowledgePublish">发布</div>
          <div class="btn" @click="close">暂存并关闭</div>
        </div>
      </div>
    </Modal>
  </div>
</template>

<script>
// 知识类型常量定义
const typeList = {
  1: "心得体会",
  2: "图片",
  3: "培训视频",
  4: "文件资料",
};

// 文件上传类型限制
const acceptList = {
  2: ".jpeg,png,.gif,.jpg", // 图片格式
  3: ".avi,.mp4,.mov,.wmv", // 视频格式
  4: ".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx", // 文档格式
};

export default {
  name: "IndividualCenter", // 个人中心组件
  data() {
    return {
      acceptList, // 文件上传类型限制列表
      typeList, // 知识类型列表
      publishModalStatus: false, // 知识发布模态框的显示状态
      title: "", // 知识标题
      content: "", // 知识内容
      type: null, // 知识类型
      file: [], // 上传的文件列表
      DataStatistics: {}, // 用户数据统计信息
      artworkUnreadCount: 0, // 未读知识数量
      commentUnreadCount: 0, // 未读评论数量
      commentUnreadList: [], // 未读评论列表
      collectList: [], // 收藏列表
    };
  },
  created() {
    // 组件创建时初始化数据
    this.getDataStatistics();
    this.getArtworkUnreadCount();
    this.getCommentUnreadCount();
    this.getCommentUnreadList();
    this.getCollectList();
  },
  methods: {
    toMessageList(id) {
      const { href } = this.$router.resolve({
        path: "/main/study/messageList",
        query: { type: id },
      });
      window.open(href, "_blank");
    },
    // 跳转到详情页
    toDetail(id) {
      const { href } = this.$router.resolve({
        path: "/main/study/detail",
        query: { artworkId: id },
      });
      window.open(href, "_blank");
    },

    // 获取收藏列表
    getCollectList() {
      this.$http
        .get("/learning/getCollectList", {
          params: {
            pageNo: 1,
            pageSize: 3, // 只获取前3条数据
          },
        })
        .then((res) => {
          this.collectList = res.body.data;
        });
    },

    // 获取未读评论列表
    getCommentUnreadList() {
      this.$http
        .get("/learning/commentUnreadList", {
          params: {
            pageNo: 1,
            pageSize: 3, // 只获取前3条数据
          },
        })
        .then((res) => {
          this.commentUnreadList = res.body.data;
        });
    },

    // 获取未读评论数量
    getCommentUnreadCount() {
      this.$http
        .get("/learning/commentUnreadCount", {
          params: { dayNum: 98 },
        })
        .then((res) => {
          this.commentUnreadCount = res.body.data;
        });
    },

    // 获取未读文章数量
    getArtworkUnreadCount() {
      this.$http.get("/learning/artworkUnreadCount").then((res) => {
        this.artworkUnreadCount = res.body.data;
      });
    },

    // 获取用户统计数据
    getDataStatistics() {
      this.$http.get("/learning/getUserStat").then((res) => {
        this.DataStatistics = res.body.data;
      });
    },

    // 发布知识
    knowledgePublish() {
      let params = new FormData();
      params.append("msgTitle", this.title);
      params.append("type", this.type);
      params.append("msgContent", this.content);
      // 遍历添加所有文件
      this.file.forEach((file) => {
        params.append("files", file);
      });
      this.$http
        .post("/learning/addArtwork", params, {
          responseType: "blob",
        })
        .then((res) => {
          console.log(res);
          if (res.body.status !== 0) {
            this.$Message.error("发布失败");
            return false;
          }
          this.publishModalStatus = false;
          this.$Message.success("发布成功");
          this.title = "";
          this.content = "";
          this.type = null;
          this.file = [];
          this.$emit('UpdataList')
        })
        .catch((e) => {
          this.$Message.error("发布失败");
        });
    },

    // 关闭发布模态框
    close() {
      this.publishModalStatus = false;
    },

    // 删除已选文件
    delFile(index) {
      this.file.splice(index, 1);
    },

    // 文件上传前的处理
    handleUpload(file) {
      this.file.push(file);
      return false; // 阻止自动上传
    },
  },

  watch: {
    // 当知识类型改变时，清空已选文件
    type() {
      this.file = [];
    },
  },
};
</script>

<style lang="less" scoped>
.btnFrame {
  justify-content: center;
  .btn {
    margin: 10px;
    width: 100px;
    &:nth-child(2) {
      background-color: #aaa;
    }
  }
}
.modal {
  .item {
    margin-bottom: 20px;
    .label {
      align-items: center;
    }
    .content {
      flex: 1;
    }
    .remark {
      width: 50px;
      justify-content: center;
      color: #aaa;
    }
  }
}
.IndividualCenter {
  overflow-y: auto;
  background-color: #fff;
  border-radius: 5px;
  width: 420px;

  .header {
    padding: 20px;
    align-items: center;
    height: 60px;
    border-bottom: 1px solid #e5e5e5;
    .title {
      font-weight: 600;
    }
  }
  .content {
    padding: 20px;
    .title {
      align-items: center;
      font-size: 14px;
      font-weight: 600;
      .dian {
        width: 8px;
        height: 8px;
        border-radius: 4px;
        background: #000;
        margin-right: 5px;
      }
    }
    .dataStatistics {
      flex-wrap: wrap;
      margin-bottom: 20px;
      .newitem {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
      }
      .item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        color: #fff;
        margin-top: 10px;
        // border: 1px solid #e5e5e5;
        padding: 10px 14px;
        height: 72px;
        border-radius: 8px;
        // border-radius: 5px;
        div {
          text-align: center;
          line-height: 24px;
        }
        .title {
          // color: #ababab;
          font-weight: 400;
        }
        .num {
          // color: #6798ed;
          font-size: 16px;
          font-weight: 600;
        }
      }
      .public {
        background: #F98058;
      }
      .readpeo {
        background: #49B9E9;
      }
      .like {
        background: #6289F2;
      }
      .mark {
        background: #5AC2CA;
      }
      .collect {
        background: #F9BE63;
      }
    }
    .notification {
      padding: 10px 0;
      .result {
        display: flex;
        justify-content: space-between;
        font-size: 14px;
        color: #ababab;
        .item {
          display: flex;
          align-items: center;
          justify-content: center;
          flex-direction: column;
          width: 151px;
          height: 52px;
          background: #E7ECF2;
          border-radius: 8px;
          border-left: 3px solid #5482EA;
          border-right: 3px solid #5482EA;
        }
        span {
          color: #000;
          font-size: 16px;
          font-weight: 400;
        }
      }
      .infoList {
        margin-top: 8px;
        padding: 22px 11px;
        background: #FFFFFF;
        box-shadow: 0px 2px 4px 0px rgba(0,0,0,0.16);
        border-radius: 8px;
        .item {
          display: flex;
          justify-content: flex-start;
          margin-top: 10px;
          font-size: 14px;
          span {
            color: #6b9aed;
          }
        }
        .item:nth-child(even) {
          background: #F4F8FF;
        }
      }
    }
    .seeMore {
      color: #5585EC;
      text-align: right;
      font-size: 14px;
    }
    .collect {
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      .item {
        margin-top: 10px;
        font-size: 14px;
        line-height: 24px;
        color: #6b9aed;
        .type {
          display: inline-block;
          padding: 0 10px;
          height: 24px;
          color: #fff;
          background-color: #169bd5;
          text-align: center;
          border-radius: 5px;
          font-weight: 600;
        }
      }
      .item:nth-child(even) {
        background: #F4F8FF;
      }
    }
  }
}
</style>
