<template>
  <div class="study flex sb">
    <div class="content">
      <div class="header flex sb">
        <div class="time flex">
          <svg-icon icon-class="重点提示-时间"></svg-icon><span>时间：</span>
          <div
            :class="['item', dayNum === -1 ? 'active' : '']"
            @click="timeChange(-1)"
          >
            全部
          </div>
          <div
            :class="['item', dayNum === v ? 'active' : '']"
            v-for="(v, k) in timeList"
            :key="k"
            @click="timeChange(v)"
          >
            {{ k }}
          </div>
          <DatePicker
            :value="dateList"
            v-if="dayNum === '99'"
            format="yyyy-MM-dd"
            type="daterange"
            split-panels
            placement="bottom-end"
            placeholder=""
            @on-change="dateChange"
            style="width: 220px;"
          ></DatePicker>
        </div>
        <div class="unit flex">
          <span class="content-title">发布单位：</span>
          <div class="content-number unitFrame">
            <Select
              ref="unitRefs"
              v-model="sendOrgId"
              :value="sendOrgId"
              style="width: 170px;"
              @on-change="changeInstitution"
              @on-open-change="openChange"
              clearable
            >
              <Option
                v-for="item in unitList"
                :value="item.organId"
                :key="item.organId"
                >{{ item.organName }}
              </Option>
            </Select>
            <SentUnit
              v-if="SentUnitStatus"
              :allList="unitList"
              @outside-click="SentUnitStatus = false"
              @confirm="changeInstitution"
            />
          </div>
        </div>
        <div class="search">
          <Input
            v-model="keyword"
            search
            @on-search="search"
            style="width: 270px;"
          >
            <Select v-model="searchType" slot="prepend" style="width: 110px;">
              <Option value="1">关键词查找</Option>
              <Option value="2">发布人查找</Option>
            </Select>
          </Input>
        </div>
      </div>
      <div class="list">
        <NoData v-if="!loading && count == 0" />
        <Spin style="margin: 100px;" v-if="loading">
          <Icon type="ios-loading" size="18" class="demo-spin-icon-load"></Icon>
          <div>Loading</div>
        </Spin>
        <template v-if="!loading">
          <ListItem
            @viewMedium="viewMedium"
            v-for="item in list"
            :key="item.artworkId"
            :info="item"
            @updataList="UpdataList"
          />
        </template>

        <Page
          v-if="count > 0 && !loading"
          :total="count"
          :current="pageNo"
          :page-size="pageSize"
          show-total
          @on-change="pageChange"
        />
      </div>
    </div>
    <IndividualCenter @UpdataList="UpdataList" />
    <ImgPreview
      v-if="ImgPreviewStatus"
      :imgList="[ImgPreviewSrc]"
      @close="ImgPreviewStatus = false"
      :type="previewType"
    />
  </div>
</template>

<script>
import ImgPreview from "@/components/imgPreview";

// 修改前
// const timeList = {
// 修改后
const timeList = {
  今日: "1",
  近一日: "98",
  近三日: "3",
  近一周: "7",
  近一月: "30",
  自定义: "99",
};
import SentUnit from "@/components/listControls/components/SentUnit.vue";
import ListItem from "./components/ListItem.vue";
import IndividualCenter from "./components/IndividualCenter.vue";
import moment from "moment";
export default {
  name: "",
  data() {
    return {
      timeList, // 时间筛选列表
      dayNum: -1, // 选中的时间类型，-1表示全部
      dateList: null, // 自定义日期范围
      SentUnitStatus: false, // 发布单位选择器的显示状态
      sendOrgId: null, // 选中的发布单位ID
      unitList: [], // 发布单位列表
      searchType: "1", // 搜索类型：1-标题，2-内容
      keyword: "", // 搜索关键词
      ImgPreviewStatus: false,
      ImgPreviewSrc: null,
      pageNo: 1, // 当前页
      pageSize: 10, // 每页显示数量
      count: 0, // 总数量
      loading: false, // 加载状态
      list: [], // 列表数据
      previewType: null,
    };
  },
  components: {
    SentUnit,
    ListItem,
    IndividualCenter,
    ImgPreview,
  },
  watch: {
    dayNum(val) {
      if (val != 99) {
        this.dateList = null;
        this.getListCount();
      }
    },
    sendOrgId(val) {
      this.getListCount();
    },
  },
  methods: {
    UpdataList() {
      this.getListCount();
    },
    pageChange(num) {
      this.pageNo = num;
      this.getListData();
    },
    getListCount() {
      this.pageNo = 1; // 重置页码
      this.count = 0; // 重置总数量
      this.loading = true; // 设置加载状态为true
      this.list = [];
      let params = {
        dayNum: this.dayNum,
        startTime: this.dateList
          ? moment(this.dateList[0]).format("YYYY-MM-DD HH:mm:ss")
          : null, //自定义开始时间，传入时间后dayNum失效
        endTime: this.dateList
          ? moment(this.dateList[1]).format("YYYY-MM-DD HH:mm:ss")
          : null, //自定义结束时间，传入时间后dayNum失效
        keyword: this.keyword, //关键词
        searchType: this.searchType, //检索位置：1 关键词、2 发布人
        organIds: this.sendOrgId, //机构id，多个逗号分割
      };
      this.$http.get("/learning/getArtworkCount", { params }).then((res) => {
        this.count = res.body.data;
        if (this.count > 0) {
          this.getListData();
        } else {
          this.loading = false;
        }
      });
    },
    getListData() {
      this.loading = true;
      this.list = [];
      let params = {
        dayNum: this.dayNum,
        startTime: this.dateList
          ? moment(this.dateList[0]).format("YYYY-MM-DD HH:mm:ss")
          : null, //自定义开始时间，传入时间后dayNum失效
        endTime: this.dateList
          ? moment(this.dateList[1]).format("YYYY-MM-DD HH:mm:ss")
          : null, //自定义结束时间，传入时间后dayNum失效
        keyword: this.keyword, //关键词
        searchType: this.searchType, //检索位置：1 关键词、2 发布人
        organIds: this.sendOrgId, //机构id，多个逗号分割
        pageNo: this.pageNo, //页数
        pageSize: this.pageSize, //页码
      };
      this.$http.get("/learning/getArtworkList", { params }).then((res) => {
        console.log(res);
        this.loading = false;
        this.list = res.body.data;
      });
    },
    viewMedium(d, type) {
      console.log(type);
      this.previewType = type;
      console.log(this.previewType);
      this.ImgPreviewSrc = d;
      this.ImgPreviewStatus = true;
    },
    search() {
      this.getListCount();
    }, // 搜索方法
    openChange(status) {
      // 发布单位选择器打开状态改变时的处理
      this.SentUnitStatus = true;
      document.body.click();
    },
    changeInstitution(val) {
      // 切换发布单位
      this.$nextTick(() => {
        this.sendOrgId = val;
      });
    },
    timeChange(k) {
      console.log(k);
      // 切换时间筛选
      this.dayNum = k;
    },
    dateChange(value) {
      // 自定义日期范围改变
      this.dateList = value;
      this.getListCount();
    },
    getSectionList(id) {
      // 获取部门列表
      let params = {
        organId: 0,
      };
      this.$http.get("/common/findDept", { params }).then((res) => {
        let result = res.body;
        if (result.status == 0) {
          let list = [
            ...(result.data[2] ? result.data[2] : []),
            ...(result.data[3] ? result.data[3] : []),
          ];
          this.unitList = list;
        } else {
          this.$Message.error(result.message);
        }
      });
    },
  },
  mounted() {
    this.getListCount();
    this.getSectionList();
    this.getLog('学习园地/首页','浏览/首页浏览');
  },
};
</script>

<style lang="less" scoped>
.study {
  padding: 10px 20px 10px 0;
  height: 100%;
  .content {
    width: 1300px;
    padding: 10px;
    background-color: #fff;
    border-radius: 5px;
    .header {
      height: 50px;
      border-bottom: 1px solid #e6e6e6;
      align-items: center;
      .time {
        align-items: center;
        flex: 1;
        .item {
          margin: 0 10px;
          cursor: pointer;
        }
        .active {
          color: blue;
        }
      }
      .unit {
        margin-right: 20px;
        .content-title {
          line-height: 32px;
        }
      }
      .svg-icon {
        width: 25px;
        height: 25px;
      }
    }
    .list {
      padding-top: 20px;
      overflow-y: auto;
      height: calc(~"100vh - 100px");
    }
  }
}
/deep/.unitFrame > .ivu-select > .ivu-select-dropdown {
  display: none;
}
</style>
